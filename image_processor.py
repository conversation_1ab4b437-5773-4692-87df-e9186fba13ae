#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像处理模块
提供图像转换、标注和保存功能
"""

import numpy as np
import cv2
import os
import json
from typing import List, Optional, Tuple
from datetime import datetime

# 导入自定义模块
from yolo_detector import Detection, YOLODetector
from object_analyzer import ObjectAnalysisResult, ObjectAnalyzer
from config import SystemConfig, PathConfig

class ImageProcessor:
    """图像处理器"""
    
    def __init__(self, config: SystemConfig):
        """
        初始化图像处理器
        
        Args:
            config: 系统配置
        """
        self.config = config
        self.yolo_config = config.yolo
        # 初始化时就创建好目录
        PathConfig.setup_directories()
    
    def frame_to_bgr_image(self, frame):
        """将彩色帧转换为BGR图像"""
        if frame is None:
            return None
            
        try:
            from pyorbbecsdk import OBFormat
            
            width = frame.get_width()
            height = frame.get_height()
            color_format = frame.get_format()
            data = np.asanyarray(frame.get_data(), dtype=np.uint8)
            
            if color_format == OBFormat.RGB:
                image = np.resize(data, (height, width, 3))
                image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
            elif color_format == OBFormat.BGR:
                image = np.resize(data, (height, width, 3))
            elif color_format == OBFormat.YUYV:
                image = np.resize(data, (height, width, 2))
                image = cv2.cvtColor(image, cv2.COLOR_YUV2BGR_YUYV)
            elif color_format == OBFormat.MJPG:
                # MJPG格式需要用cv2.imdecode解码
                image = cv2.imdecode(data, cv2.IMREAD_COLOR)
                if image is None:
                    return None
            elif color_format == OBFormat.UYVY:
                image = np.resize(data, (height, width, 2))
                image = cv2.cvtColor(image, cv2.COLOR_YUV2BGR_UYVY)
            else:
                return None
                
            return image
            
        except Exception as e:
            return None
    
    def process_color_frame(self, color_frame) -> Optional[np.ndarray]:
        """
        处理彩色帧（参考depth_viewer_plus.py的方法）
        
        Args:
            color_frame: 彩色帧对象
            
        Returns:
            处理后的BGR图像数组
        """
        try:
            color_width = color_frame.get_width()
            color_height = color_frame.get_height()
            color_data = np.frombuffer(color_frame.get_data(), dtype=np.uint8)
            color_format = color_frame.get_format()
            
            # 尝试作为JPEG数据解码（MJPG格式）
            try:
                color_image = cv2.imdecode(color_data, cv2.IMREAD_COLOR)
                if color_image is not None:
                    return color_image
            except Exception:
                pass
            
            # 如果JPEG解码失败，尝试作为原始RGB数据
            expected_size = color_width * color_height * 3
            if len(color_data) == expected_size:
                try:
                    color_data = color_data.reshape((color_height, color_width, 3))
                    
                    # 根据格式转换
                    if color_format == 1:  # RGB格式
                        color_image = cv2.cvtColor(color_data, cv2.COLOR_RGB2BGR)
                    else:  # BGR格式
                        color_image = color_data
                    
                    return color_image
                    
                except Exception:
                    pass
            
            # 如果所有方法都失败，使用原来的frame_to_bgr_image函数
            return self.frame_to_bgr_image(color_frame)
            
        except Exception:
            return None
    
    def annotate_analysis_results(self, image: np.ndarray, analysis_results: List[ObjectAnalysisResult], scale_factor: float = 1.0):
        """
        在图像上标注物体分析结果
        
        Args:
            image: 输入图像
            analysis_results: 分析结果列表
            scale_factor: 缩放因子，用于调整坐标
        """
        for result in analysis_results:
            if not hasattr(result, 'bbox') or result.bbox is None:
                continue
                
            x1, y1, x2, y2 = result.bbox
            
            # 根据缩放因子调整坐标
            if scale_factor != 1.0:
                x1 = int(x1 * scale_factor)
                y1 = int(y1 * scale_factor)
                x2 = int(x2 * scale_factor)
                y2 = int(y2 * scale_factor)
            
            # 简化标签文本 - 只显示基本信息
            if hasattr(result, 'is_real_object'):
                is_real = result.is_real_object
                confidence = result.confidence if hasattr(result, 'confidence') else 0.0
                # 简化标签：Real/Flat + 置信度
                label = f"{'Real' if is_real else 'Flat'} {confidence:.2f}"
            else:
                label = "Failed"
            
            # 获取点云统计信息 - 简化显示
            point_count = result.point_count if hasattr(result, 'point_count') else 0
            height_variance = result.height_variance if hasattr(result, 'height_variance') else 0.0
            # 简化详细信息
            details = f"Pts:{point_count} Var:{height_variance:.1f}"
            
            # 获取颜色
            if hasattr(result, 'is_real_object'):
                color = (0, 255, 0) if result.is_real_object else (255, 150, 0) # 绿色代表真实，橙色代表平面
            else:
                color = (0, 0, 255)  # 红色代表失败
            
            # 绘制文本 - 优化字体大小和粗细
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.3  # 字体大小
            thickness = 1     # 字体粗细

            # 绘制主标签
            cv2.putText(image, label, (x1, y1 - 30), font, font_scale, color, thickness, cv2.LINE_AA)
            # 绘制详细信息
            cv2.putText(image, details, (x1, y1 - 10), font, font_scale * 0.8, color, thickness, cv2.LINE_AA)

    def draw_masks(self, image: np.ndarray, detections: List[Detection], alpha: float = 0.4):
        """
        在图像上绘制分割掩码
        
        Args:
            image: 输入图像 (BGR)
            detections: 检测结果列表
            alpha: 掩码的透明度
        """
        if not detections:
            return

        overlay = image.copy()
        image_height, image_width = image.shape[:2]
        
        for i, detection in enumerate(detections):
            if detection.mask is not None:
                # 为每个实例选择一个颜色
                color_bgr = self._get_color(i)
                
                # 检查掩码尺寸是否与图像匹配
                mask_height, mask_width = detection.mask.shape
                
                if mask_height != image_height or mask_width != image_width:
                    # 调整掩码尺寸以匹配图像
                    print(f"🔄 调整掩码尺寸: {mask_width}x{mask_height} -> {image_width}x{image_height}")
                    resized_mask = cv2.resize(
                        detection.mask, 
                        (image_width, image_height), 
                        interpolation=cv2.INTER_NEAREST
                    )
                    # 确保掩码是二进制的
                    mask_indices = (resized_mask > 0.5).astype(bool)
                else:
                    # 掩码尺寸匹配，直接使用
                    mask_indices = detection.mask.astype(bool)
                
                # 将掩码应用到图像上
                overlay[mask_indices] = color_bgr
        
        # 将覆盖层与原始图像混合
        cv2.addWeighted(overlay, alpha, image, 1 - alpha, 0, image)

    def annotate_detections(self, image: np.ndarray, detections: List[Detection]):
        """在图像上标注YOLO检测结果"""
        for detection in detections:
            x1, y1, x2, y2 = detection.bbox
            # 加粗显示框
            cv2.rectangle(image, (x1, y1), (x2, y2), (255, 0, 0), 1)
            # 简化标签：只显示类名和置信度，优化字体
            label = f"{detection.class_name} {detection.confidence:.2f}"
            cv2.putText(image, label, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 1)
    
    def add_ui_overlay(self, image: np.ndarray):
        """在图像上添加UI覆盖层"""
        # 在图像上添加英文提示文字，优化字体
        cv2.putText(image, "Press SPACE to capture frame", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        cv2.putText(image, "Press Q to quit", (10, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        cv2.putText(image, "Press S to save frame", (10, 90), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    
    def save_frame_results(self, frame_timestamp: str, image: np.ndarray, 
                          results: List[ObjectAnalysisResult], detections: List[Detection]
    ) -> Tuple[Optional[str], Optional[str]]:
        """保存帧的图像和分析结果"""
        try:
            # 修正：使用PathConfig获取数据目录
            output_dir = PathConfig.DATA_DIR
            
            # 1. 保存标注后的图像
            img_filename = f"res_{frame_timestamp}.jpg"
            img_path = os.path.join(output_dir, img_filename)
            cv2.imwrite(img_path, image)
            print(f"🖼️  标注图像已保存: {img_path}")
            
            # 2. 保存JSON分析结果
            json_filename = f"analysis_{frame_timestamp}.json"
            json_path = os.path.join(output_dir, json_filename)
            
            # 创建简化的分析结果字典
            analysis_results = []
            for result in results:
                result_dict = {}
                
                # 尝试获取基本属性
                if hasattr(result, 'class_id'):
                    result_dict['class_id'] = result.class_id
                if hasattr(result, 'class_name'):
                    result_dict['class_name'] = result.class_name
                if hasattr(result, 'is_real_object'):
                    result_dict['is_real_object'] = result.is_real_object
                if hasattr(result, 'confidence'):
                    result_dict['confidence'] = result.confidence
                if hasattr(result, 'height_variance'):
                    result_dict['height_variance'] = result.height_variance
                if hasattr(result, 'point_count'):
                    result_dict['point_count'] = result.point_count
                
                analysis_results.append(result_dict)
            
            # 创建检测结果字典
            detection_results = []
            for det in detections:
                if hasattr(det, 'to_dict'):
                    detection_results.append(det.to_dict())
                else:
                    det_dict = {
                        'class_id': det.class_id,
                        'class_name': det.class_name,
                        'confidence': det.confidence,
                        'bbox': det.bbox
                    }
                    detection_results.append(det_dict)
            
            frame_data = {
                'timestamp': frame_timestamp,
                'detections': detection_results,
                'analysis_results': analysis_results,
                'summary': {
                    'total_objects': len(detections),
                    'real_objects': sum(1 for r in results if hasattr(r, 'is_real_object') and r.is_real_object),
                    'flat_objects': sum(1 for r in results if hasattr(r, 'is_real_object') and not r.is_real_object)
                }
            }
            
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(frame_data, f, indent=2, ensure_ascii=False)
            
            print(f"💾 分析结果已保存: {json_path}")
            
            return img_path, json_path
            
        except Exception as e:
            print(f"❌ 保存帧失败: {e}")
            return None, None
    
    def save_current_frame(self, frame_timestamp: str, color_image: np.ndarray) -> Optional[str]:
        """保存当前帧的图像"""
        try:
            # 修正：使用PathConfig获取数据目录
            output_dir = PathConfig.DATA_DIR
            filename = f"capture_{frame_timestamp}.jpg"
            save_path = os.path.join(output_dir, filename)
            cv2.imwrite(save_path, color_image)
            print(f"📸 当前帧图像已保存: {save_path}")
            return save_path
        except Exception as e:
            print(f"❌ 保存当前帧失败: {e}")
            return None
    
    def _get_point_cloud_summary(self, analysis_results: List[ObjectAnalysisResult]) -> dict:
        """获取点云数据摘要"""
        summary = {
            'total_objects': len(analysis_results),
            'point_cloud_stats': []
        }
        
        for i, result in enumerate(analysis_results):
            obj_stats = {
                'object_id': i + 1,
                'class_name': result.class_name,
                'is_real_object': result.is_real_object,
                'height_variance': result.height_variance,
                'confidence_score': result.confidence_score,
                'bbox': result.bbox
            }
            
            # 添加点云统计信息（如果有的话）
            if hasattr(result, 'point_cloud_stats'):
                obj_stats['point_cloud_stats'] = result.point_cloud_stats
            
            summary['point_cloud_stats'].append(obj_stats)
        
        return summary 

    def _get_color(self, index: int) -> Tuple[int, int, int]:
        """为不同物体获取一个固定的颜色"""
        # 颜色列表 (BGR格式)
        colors = [
            (0, 0, 255), # 红色
            (0, 255, 0), # 绿色
            (255, 0, 0), # 蓝色
            (255, 255, 0), # 黄色
            (255, 0, 255), # 紫色
            (0, 255, 255), # 青色
            (255, 192, 203), # 粉色
        ]
        return colors[index % len(colors)] 