#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进版多帧检测策略：低阈值输入，高阈值输出
验证置信度过滤问题是否得到解决，多帧融合功能是否正常工作
"""

import numpy as np
import sys
import os

def test_low_input_high_output_strategy():
    """测试低阈值输入，高阈值输出策略"""
    print("🧪 测试低阈值输入，高阈值输出策略...")
    
    try:
        from config import default_config
        from multi_frame_yolo_detector import MultiFrameYOLODetector
        from yolo_detector import Detection
        
        # 创建改进版多帧检测器
        detector = MultiFrameYOLODetector(config=default_config)
        
        print(f"📋 策略配置:")
        print(f"   输入置信度阈值: {detector.input_confidence_threshold} (低阈值，保留更多候选)")
        print(f"   输出置信度阈值: {detector.output_confidence_threshold} (高阈值，确保输出质量)")
        print(f"   IoU匹配阈值: {detector.iou_match_threshold} (>0.5判断同一物体)")
        print(f"   IoU稳定性阈值: {detector.iou_stability_threshold} (连续两帧<0.3重置)")
        
        # 模拟不同置信度的检测结果
        test_detections = [
            Detection(
                box=np.array([100, 100, 200, 200]),
                confidence=0.05,  # 极低置信度
                class_id=0,
                class_name="very_low_conf",
                mask=np.ones((100, 100), dtype=bool)
            ),
            Detection(
                box=np.array([300, 300, 400, 400]),
                confidence=0.12,  # 低置信度但超过输入阈值
                class_id=1,
                class_name="low_conf_object",
                mask=np.ones((100, 100), dtype=bool)
            ),
            Detection(
                box=np.array([500, 500, 600, 600]),
                confidence=0.25,  # 中等置信度
                class_id=2,
                class_name="medium_conf_object",
                mask=np.ones((100, 100), dtype=bool)
            ),
            Detection(
                box=np.array([700, 700, 800, 800]),
                confidence=0.8,  # 高置信度
                class_id=3,
                class_name="high_conf_object",
                mask=np.ones((100, 100), dtype=bool)
            )
        ]
        
        print(f"\n🔍 原始检测结果: {len(test_detections)} 个")
        for det in test_detections:
            print(f"   {det.class_name}: 置信度 {det.confidence:.3f}")
        
        # 测试输入过滤
        input_filtered = [
            det for det in test_detections 
            if det.confidence >= detector.input_confidence_threshold
        ]
        
        print(f"\n📥 输入过滤后: {len(input_filtered)} 个 (阈值: {detector.input_confidence_threshold})")
        for det in input_filtered:
            print(f"   保留: {det.class_name} (置信度: {det.confidence:.3f})")
        
        # 验证改进效果
        if len(input_filtered) > len(test_detections) * 0.5:
            print("✅ 低阈值输入策略有效：保留了足够多的候选进入多帧处理")
        else:
            print("⚠️ 输入阈值可能仍然过高")
        
        return len(input_filtered) > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fusion_confidence_calculation():
    """测试融合置信度计算"""
    print("\n🧪 测试融合置信度计算...")
    
    try:
        from multi_frame_yolo_detector import TrackedObject
        from yolo_detector import Detection
        
        # 创建跟踪对象
        tracked_obj = TrackedObject(
            object_id="test_001",
            class_name="test_object",
            class_id=0
        )
        
        # 模拟多帧检测结果（置信度逐渐提升）
        confidences = [0.15, 0.20, 0.25, 0.30, 0.35]
        
        for i, conf in enumerate(confidences):
            detection = Detection(
                box=np.array([100 + i*2, 100 + i*2, 200 + i*2, 200 + i*2]),
                confidence=conf,
                class_id=0,
                class_name="test_object",
                mask=np.ones((100, 100), dtype=bool)
            )
            tracked_obj.add_detection(detection, i+1)
        
        # 测试融合置信度计算
        fusion_conf = tracked_obj.get_fusion_confidence(3)  # 最近3帧
        avg_conf = tracked_obj.get_average_confidence()
        
        print(f"   单帧置信度序列: {confidences}")
        print(f"   平均置信度: {avg_conf:.3f}")
        print(f"   融合置信度 (最近3帧): {fusion_conf:.3f}")
        
        # 测试稳定性判断
        is_stable = tracked_obj.is_stable_with_fusion_confidence(
            stable_threshold=2,
            output_confidence_threshold=0.3,
            n_frames=3
        )
        
        print(f"   是否达到稳定标准: {is_stable}")
        print(f"   连续帧数: {tracked_obj.consecutive_frames}")
        
        # 验证融合置信度是否合理
        if fusion_conf > avg_conf:
            print("✅ 融合置信度计算正常：时间加权提升了置信度")
        else:
            print("⚠️ 融合置信度计算可能有问题")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_frame_processing_pipeline():
    """测试完整的多帧处理管道"""
    print("\n🧪 测试完整的多帧处理管道...")
    
    try:
        from config import default_config
        from multi_frame_yolo_detector import MultiFrameYOLODetector
        from yolo_detector import Detection
        
        # 创建检测器
        detector = MultiFrameYOLODetector(config=default_config)
        
        # 模拟5帧检测序列
        frame_sequences = []
        
        # 帧1-3：低置信度但稳定的物体
        for i in range(3):
            detections = [
                Detection(
                    box=np.array([100 + i*2, 100 + i*2, 200 + i*2, 200 + i*2]),
                    confidence=0.15 + i*0.05,  # 0.15, 0.20, 0.25
                    class_id=0,
                    class_name="stable_low_conf",
                    mask=np.ones((100, 100), dtype=bool)
                )
            ]
            frame_sequences.append(detections)
        
        # 帧4-5：置信度提升
        for i in range(2):
            detections = [
                Detection(
                    box=np.array([106 + i*2, 106 + i*2, 206 + i*2, 206 + i*2]),
                    confidence=0.30 + i*0.05,  # 0.30, 0.35
                    class_id=0,
                    class_name="stable_low_conf",
                    mask=np.ones((100, 100), dtype=bool)
                )
            ]
            frame_sequences.append(detections)
        
        print(f"   模拟 {len(frame_sequences)} 帧检测序列")
        
        # 逐帧处理
        results = []
        for i, detections in enumerate(frame_sequences):
            print(f"\n   --- 处理第 {i+1} 帧 ---")
            
            # 模拟单帧检测（跳过实际YOLO调用）
            detector.frame_count = i
            detector._update_tracking(detections)
            
            # 获取稳定检测结果
            stable_detections = detector._get_stable_detections()
            
            # 统计信息
            stats = detector.get_tracking_stats()
            
            print(f"     当前帧检测: {len(detections)} 个")
            print(f"     稳定物体: {len(stable_detections)} 个")
            print(f"     跟踪对象总数: {stats['total_tracked_objects']}")
            
            # 显示稳定物体的融合置信度
            for obj in detector.tracked_objects.values():
                if obj.is_stable:
                    fusion_conf = obj.get_fusion_confidence(3)
                    print(f"     稳定物体 {obj.class_name}: 融合置信度 {fusion_conf:.3f}")
            
            results.append({
                'frame': i+1,
                'input_detections': len(detections),
                'stable_detections': len(stable_detections),
                'tracked_objects': stats['total_tracked_objects']
            })
        
        # 验证处理效果
        final_stable = results[-1]['stable_detections']
        if final_stable > 0:
            print(f"\n✅ 多帧处理管道正常：最终输出 {final_stable} 个稳定物体")
            print("   低置信度候选通过多帧融合成功转化为稳定输出")
        else:
            print("\n⚠️ 多帧处理管道可能有问题：没有稳定物体输出")
        
        return final_stable > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_iou_stability_check():
    """测试IoU稳定性检查"""
    print("\n🧪 测试IoU稳定性检查...")
    
    try:
        from multi_frame_yolo_detector import TrackedObject
        from yolo_detector import Detection
        
        # 创建跟踪对象
        tracked_obj = TrackedObject(
            object_id="test_001",
            class_name="test_object",
            class_id=0
        )
        
        # 添加第一个检测
        detection1 = Detection(
            box=np.array([100, 100, 200, 200]),
            confidence=0.2,
            class_id=0,
            class_name="test_object",
            mask=np.ones((100, 100), dtype=bool)
        )
        tracked_obj.add_detection(detection1, 1)
        
        # 测试稳定移动（IoU > 0.3）
        detection2 = Detection(
            box=np.array([105, 105, 205, 205]),  # 小幅移动
            confidence=0.25,
            class_id=0,
            class_name="test_object",
            mask=np.ones((100, 100), dtype=bool)
        )
        
        is_stable = tracked_obj.check_iou_stability(detection2, 0.3)
        print(f"   小幅移动 IoU稳定性: {is_stable} (应该为True)")
        
        # 测试大幅跳动（IoU < 0.3）
        detection3 = Detection(
            box=np.array([400, 400, 500, 500]),  # 大幅跳动
            confidence=0.3,
            class_id=0,
            class_name="test_object",
            mask=np.ones((100, 100), dtype=bool)
        )
        
        is_stable = tracked_obj.check_iou_stability(detection3, 0.3)
        print(f"   大幅跳动 IoU稳定性: {is_stable} (应该为False)")
        
        if not is_stable:
            print("✅ IoU稳定性检查正常：能够识别位置大跳动")
        else:
            print("⚠️ IoU稳定性检查可能有问题")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试改进版多帧检测策略...")
    print("   核心改进：低阈值输入(0.1)，高阈值输出(0.3)")
    
    tests = [
        ("低阈值输入高阈值输出策略", test_low_input_high_output_strategy),
        ("融合置信度计算", test_fusion_confidence_calculation),
        ("完整多帧处理管道", test_multi_frame_processing_pipeline),
        ("IoU稳定性检查", test_iou_stability_check)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"测试: {test_name}")
        print('='*60)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("改进版策略测试总结")
    print('='*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 改进版策略测试全部通过！")
        
        print("\n💡 关键改进验证:")
        print("   ✅ 低阈值输入(0.1)：更多YOLO候选进入多帧处理")
        print("   ✅ 高阈值输出(0.3)：融合置信度确保输出质量")
        print("   ✅ IoU匹配(>0.5)：准确判断同一物体")
        print("   ✅ IoU稳定性(<0.3重置)：避免抖动物体被当成稳定")
        print("   ✅ 融合置信度计算：时间加权提升置信度")
        
        print("\n🎯 解决的核心问题:")
        print("   📈 置信度过滤过严 → 低阈值输入保留更多候选")
        print("   📉 多帧融合无效 → 候选有机会进入时序处理")
        print("   🎯 输出质量控制 → 高阈值输出确保稳定性")
        print("   🔄 掩膜抖动减少 → IoU稳定性检查避免跳动")
        
        print("\n🚀 预期实际效果:")
        print("   在YOLOv8m-seg场景下，多帧融合功能将正常工作")
        print("   低置信度但连续出现的物体能够被稳定识别")
        print("   传递给深度分析器的掩膜更加稳定和准确")
        
    else:
        print("⚠️ 部分测试失败，请检查相关实现。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
