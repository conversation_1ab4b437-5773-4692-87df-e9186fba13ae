#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化后的多帧检测系统测试
验证置信度过滤策略优化、IoU稳定性检查和掩膜融合改进的效果
"""

import numpy as np
import sys
import os

def test_optimized_config():
    """测试优化后的配置"""
    print("🧪 测试优化后的配置...")
    
    try:
        from config import default_config
        
        mf_config = default_config.multi_frame_detection
        
        print("📋 优化后的多帧检测配置:")
        print(f"   启用状态: {mf_config.enabled}")
        print(f"   融合帧数: {mf_config.n_frames}")
        print(f"   稳定阈值: {mf_config.stable_threshold} 帧")
        print(f"   初始置信度阈值: {mf_config.confidence_threshold} (宽松过滤)")
        print(f"   稳定物体最低置信度: {mf_config.min_stable_confidence}")
        print(f"   IoU阈值: {mf_config.iou_threshold}")
        print(f"   IoU稳定性阈值: {mf_config.iou_stability_threshold}")
        print(f"   IoU稳定性检查: {mf_config.enable_iou_stability_check}")
        print(f"   融合方法: {mf_config.fusion_method}")
        print(f"   最大丢失帧数: {mf_config.max_missing_frames}")
        
        # 验证配置合理性
        assert mf_config.confidence_threshold <= 0.2, "初始置信度阈值应该较低"
        assert mf_config.min_stable_confidence >= 0.3, "稳定物体置信度阈值应该较高"
        assert mf_config.iou_stability_threshold >= 0.2, "IoU稳定性阈值应该合理"
        
        print("✅ 配置验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_iou_stability_check():
    """测试IoU稳定性检查功能"""
    print("\n🧪 测试IoU稳定性检查功能...")
    
    try:
        from multi_frame_yolo_detector import TrackedObject
        from yolo_detector import Detection
        
        # 创建跟踪对象
        tracked_obj = TrackedObject(
            object_id="test_001",
            class_name="test_object",
            class_id=0
        )
        
        # 添加第一个检测（基准位置）
        detection1 = Detection(
            box=np.array([100, 100, 200, 200]),
            confidence=0.8,
            class_id=0,
            class_name="test_object",
            mask=np.ones((100, 100), dtype=bool)
        )
        tracked_obj.add_detection(detection1, 1, detection1.mask)
        
        # 测试稳定移动（小幅移动）
        detection2 = Detection(
            box=np.array([105, 105, 205, 205]),  # 小幅移动
            confidence=0.7,
            class_id=0,
            class_name="test_object",
            mask=np.ones((100, 100), dtype=bool)
        )
        
        is_stable = tracked_obj.check_iou_stability(detection2, 0.3)
        print(f"   小幅移动稳定性检查: {is_stable} (应该为True)")
        
        # 测试大幅跳动
        detection3 = Detection(
            box=np.array([300, 300, 400, 400]),  # 大幅跳动
            confidence=0.6,
            class_id=0,
            class_name="test_object",
            mask=np.ones((100, 100), dtype=bool)
        )
        
        is_stable = tracked_obj.check_iou_stability(detection3, 0.3)
        print(f"   大幅跳动稳定性检查: {is_stable} (应该为False)")
        
        print("✅ IoU稳定性检查功能正常")
        return True
        
    except Exception as e:
        print(f"❌ IoU稳定性检查测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mask_smoothing():
    """测试掩膜平滑功能"""
    print("\n🧪 测试掩膜平滑功能...")
    
    try:
        from multi_frame_yolo_detector import TrackedObject
        
        # 创建跟踪对象
        tracked_obj = TrackedObject(
            object_id="test_001",
            class_name="test_object",
            class_id=0
        )
        
        # 创建带噪声的掩膜
        mask_shape = (100, 100)
        
        # 掩膜1：主要区域 + 一些噪点
        mask1 = np.zeros(mask_shape, dtype=bool)
        mask1[30:70, 30:70] = True  # 主要区域
        mask1[10:15, 10:15] = True  # 噪点1
        mask1[80:85, 80:85] = True  # 噪点2
        
        # 掩膜2：主要区域 + 不同的噪点
        mask2 = np.zeros(mask_shape, dtype=bool)
        mask2[32:72, 32:72] = True  # 主要区域（稍微偏移）
        mask2[15:20, 15:20] = True  # 噪点3
        
        # 掩膜3：主要区域 + 空洞
        mask3 = np.zeros(mask_shape, dtype=bool)
        mask3[28:68, 28:68] = True  # 主要区域
        mask3[45:55, 45:55] = False  # 空洞
        
        # 测试平滑功能
        smooth_mask = tracked_obj._smooth_mask(mask1)
        print(f"   原始掩膜面积: {np.sum(mask1)}")
        print(f"   平滑后掩膜面积: {np.sum(smooth_mask)}")
        
        # 测试融合功能
        masks = [mask1, mask2, mask3]
        confidences = [0.8, 0.7, 0.6]
        
        # 多数投票融合
        fused_majority = tracked_obj._majority_vote_fusion(masks)
        print(f"   多数投票融合面积: {np.sum(fused_majority)}")
        
        # 置信度加权融合
        fused_weighted = tracked_obj._confidence_weighted_fusion(masks, confidences)
        print(f"   置信度加权融合面积: {np.sum(fused_weighted)}")
        
        print("✅ 掩膜平滑和融合功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 掩膜平滑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_confidence_filtering_strategy():
    """测试置信度过滤策略"""
    print("\n🧪 测试置信度过滤策略...")
    
    try:
        from config import default_config
        from multi_frame_yolo_detector import MultiFrameYOLODetector
        from yolo_detector import Detection
        
        # 创建多帧检测器
        detector = MultiFrameYOLODetector(config=default_config)
        
        # 模拟不同置信度的检测结果
        detections = [
            Detection(
                box=np.array([100, 100, 200, 200]),
                confidence=0.1,  # 很低置信度
                class_id=0,
                class_name="low_conf_object",
                mask=np.ones((100, 100), dtype=bool)
            ),
            Detection(
                box=np.array([300, 300, 400, 400]),
                confidence=0.2,  # 中等置信度
                class_id=1,
                class_name="medium_conf_object",
                mask=np.ones((100, 100), dtype=bool)
            ),
            Detection(
                box=np.array([500, 500, 600, 600]),
                confidence=0.8,  # 高置信度
                class_id=2,
                class_name="high_conf_object",
                mask=np.ones((100, 100), dtype=bool)
            )
        ]
        
        print(f"   原始检测数量: {len(detections)}")
        
        # 过滤低置信度检测
        filtered_detections = [
            det for det in detections 
            if det.confidence >= detector.confidence_threshold
        ]
        
        print(f"   过滤后检测数量: {len(filtered_detections)}")
        print(f"   置信度阈值: {detector.confidence_threshold}")
        
        # 验证过滤效果
        if len(filtered_detections) > 0:
            print("✅ 置信度过滤策略允许候选进入多帧处理")
            
            # 显示过滤后的检测
            for det in filtered_detections:
                print(f"     保留: {det.class_name} (置信度: {det.confidence:.3f})")
        else:
            print("⚠️ 所有检测都被过滤，可能需要进一步降低阈值")
        
        return len(filtered_detections) > 0
        
    except Exception as e:
        print(f"❌ 置信度过滤策略测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_stability_judgment():
    """测试稳定性判断机制"""
    print("\n🧪 测试稳定性判断机制...")
    
    try:
        from config import default_config
        from multi_frame_yolo_detector import MultiFrameYOLODetector
        from yolo_detector import Detection
        
        # 创建多帧检测器
        detector = MultiFrameYOLODetector(config=default_config)
        
        # 模拟连续帧的检测结果
        test_image = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # 模拟稳定物体（高置信度，连续出现）
        stable_detections = []
        for i in range(5):
            detection = Detection(
                box=np.array([100 + i*2, 100 + i*2, 200 + i*2, 200 + i*2]),  # 轻微移动
                confidence=0.7 + i*0.05,  # 逐渐增加的置信度
                class_id=0,
                class_name="stable_object",
                mask=np.ones((100, 100), dtype=bool)
            )
            stable_detections.append([detection])
        
        # 模拟不稳定物体（低置信度，间歇出现）
        unstable_detections = []
        for i in range(5):
            if i % 2 == 0:  # 间歇出现
                detection = Detection(
                    box=np.array([300, 300, 400, 400]),
                    confidence=0.2,  # 低置信度
                    class_id=1,
                    class_name="unstable_object",
                    mask=np.ones((100, 100), dtype=bool)
                )
                unstable_detections.append([detection])
            else:
                unstable_detections.append([])  # 空帧
        
        print("   模拟稳定物体检测序列...")
        stable_results = []
        for i, detections in enumerate(stable_detections):
            detector._update_tracking(detections)
            stable_objects = [obj for obj in detector.tracked_objects.values() if obj.is_stable]
            stable_results.append(len(stable_objects))
            print(f"     帧 {i+1}: 稳定物体数量 = {len(stable_objects)}")
        
        # 重置检测器
        detector.reset_tracking()
        
        print("   模拟不稳定物体检测序列...")
        unstable_results = []
        for i, detections in enumerate(unstable_detections):
            detector._update_tracking(detections)
            stable_objects = [obj for obj in detector.tracked_objects.values() if obj.is_stable]
            unstable_results.append(len(stable_objects))
            print(f"     帧 {i+1}: 稳定物体数量 = {len(stable_objects)}")
        
        # 验证结果
        final_stable_count = stable_results[-1] if stable_results else 0
        final_unstable_count = unstable_results[-1] if unstable_results else 0
        
        print(f"   稳定物体序列最终稳定数量: {final_stable_count}")
        print(f"   不稳定物体序列最终稳定数量: {final_unstable_count}")
        
        success = final_stable_count > 0 and final_unstable_count == 0
        if success:
            print("✅ 稳定性判断机制工作正常")
        else:
            print("⚠️ 稳定性判断机制可能需要调整")
        
        return success
        
    except Exception as e:
        print(f"❌ 稳定性判断测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始优化后的多帧检测系统测试...")
    
    tests = [
        ("优化后的配置", test_optimized_config),
        ("IoU稳定性检查", test_iou_stability_check),
        ("掩膜平滑功能", test_mask_smoothing),
        ("置信度过滤策略", test_confidence_filtering_strategy),
        ("稳定性判断机制", test_stability_judgment)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*50}")
    print("优化效果总结")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有优化测试通过！")
        
        print("\n💡 优化效果:")
        print("   ✅ 置信度过滤策略优化：降低初始阈值到0.15，让更多候选进入多帧处理")
        print("   ✅ IoU稳定性检查：避免位置大跳动的错误目标被稳定化")
        print("   ✅ 掩膜融合算法改进：包含边缘平滑和形态学处理")
        print("   ✅ 多维度稳定性判断：结合帧数、置信度、IoU稳定性")
        
        print("\n🎯 预期改进效果:")
        print("   📈 更多YOLO掩膜有机会进入时序融合处理")
        print("   📉 减少因单帧掩膜边缘抖动导致的不稳定")
        print("   🎯 提高稳定物体识别的准确性")
        print("   🔄 更平滑、稳定的掩膜输出传递给深度分析器")
        
    else:
        print("⚠️ 部分优化测试失败，请检查相关实现。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
