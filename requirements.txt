# YOLO点云联合检测系统依赖

# 核心依赖
ultralytics>=8.0.0          # YOLOv8
opencv-python>=4.5.0        # OpenCV
numpy>=1.20.0               # 数值计算
matplotlib>=3.3.0           # 可视化
scikit-learn>=1.0.0         # 机器学习算法

# 可选依赖（根据实际相机选择）
pyorbbecsdk                 # 奥比中光相机SDK
pyrealsense2                # Intel RealSense相机SDK

# 其他工具
pillow>=8.0.0               # 图像处理
scipy>=1.7.0                # 科学计算
tqdm>=4.60.0                # 进度条

# 开发依赖（可选）
pytest>=6.0.0               # 测试框架
black>=21.0.0               # 代码格式化
flake8>=3.8.0               # 代码检查 