#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多帧检测系统测试脚本
测试多帧检测系统的性能和稳定性
"""

import cv2
import numpy as np
import time
import os
from typing import List

from config import default_config
from multi_frame_yolo_detector import MultiFrameYOLODetector
from yolo_detector import YOLODetector


def create_test_image_sequence(num_frames: int = 10) -> List[np.ndarray]:
    """
    创建测试图像序列
    模拟物体移动、出现、消失的场景
    """
    images = []
    
    for i in range(num_frames):
        # 创建基础图像
        image = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # 添加背景纹理
        noise = np.random.randint(0, 50, (480, 640, 3), dtype=np.uint8)
        image = cv2.add(image, noise)
        
        # 物体1：持续移动的红色方块（稳定物体）
        x1 = 50 + i * 10
        if x1 < 550:
            cv2.rectangle(image, (x1, 100), (x1 + 80, 180), (0, 0, 255), -1)
        
        # 物体2：间歇出现的绿色圆形（不稳定物体）
        if i % 3 != 0:  # 每3帧消失一次
            cv2.circle(image, (300, 250), 40, (0, 255, 0), -1)
        
        # 物体3：后期出现的蓝色三角形
        if i >= 5:
            pts = np.array([[400, 350], [450, 400], [350, 400]], np.int32)
            cv2.fillPoly(image, [pts], (255, 0, 0))
        
        # 物体4：短暂出现的黄色椭圆（噪声物体）
        if 3 <= i <= 4:
            cv2.ellipse(image, (500, 150), (30, 20), 0, 0, 360, (0, 255, 255), -1)
        
        images.append(image)
    
    return images


def test_single_frame_vs_multi_frame():
    """
    对比单帧检测和多帧检测的性能
    """
    print("🧪 开始单帧 vs 多帧检测对比测试...")
    
    # 创建测试图像序列
    test_images = create_test_image_sequence(10)
    
    # 初始化检测器
    single_frame_detector = YOLODetector(default_config)
    multi_frame_detector = MultiFrameYOLODetector(
        config=default_config,
        n_frames=3,
        stable_threshold=2,
        confidence_threshold=0.3,  # 降低阈值以便检测到测试图像中的简单形状
        fusion_method="majority_vote"
    )
    
    print(f"📊 测试图像序列: {len(test_images)} 帧")
    
    # 单帧检测测试
    print("\n🔍 单帧检测测试:")
    single_frame_times = []
    single_frame_detections = []
    
    for i, image in enumerate(test_images):
        start_time = time.time()
        detections = single_frame_detector.detect_objects(image)
        detection_time = time.time() - start_time
        
        single_frame_times.append(detection_time)
        single_frame_detections.append(len(detections))
        
        print(f"   帧 {i+1}: {len(detections)} 个检测, 用时 {detection_time:.3f}s")
    
    # 多帧检测测试
    print("\n🎯 多帧检测测试:")
    multi_frame_times = []
    current_detections_list = []
    stable_detections_list = []
    
    for i, image in enumerate(test_images):
        start_time = time.time()
        current_detections, stable_detections = multi_frame_detector.detect_objects_multi_frame(image)
        detection_time = time.time() - start_time
        
        multi_frame_times.append(detection_time)
        current_detections_list.append(len(current_detections))
        stable_detections_list.append(len(stable_detections))
        
        stats = multi_frame_detector.get_tracking_stats()
        print(f"   帧 {i+1}: 当前{len(current_detections)}个, 稳定{len(stable_detections)}个, "
              f"跟踪{stats['total_tracked_objects']}个, 用时 {detection_time:.3f}s")
    
    # 性能统计
    print("\n📊 性能对比统计:")
    avg_single_time = np.mean(single_frame_times)
    avg_multi_time = np.mean(multi_frame_times)
    
    print(f"   单帧检测平均用时: {avg_single_time:.3f}s")
    print(f"   多帧检测平均用时: {avg_multi_time:.3f}s")
    print(f"   时间开销增加: {((avg_multi_time - avg_single_time) / avg_single_time * 100):.1f}%")
    
    print(f"\n   单帧检测平均数量: {np.mean(single_frame_detections):.1f}")
    print(f"   多帧当前检测平均数量: {np.mean(current_detections_list):.1f}")
    print(f"   多帧稳定检测平均数量: {np.mean(stable_detections_list):.1f}")
    
    # 稳定性分析
    single_frame_variance = np.var(single_frame_detections)
    stable_detections_variance = np.var(stable_detections_list)
    
    print(f"\n   单帧检测数量方差: {single_frame_variance:.2f}")
    print(f"   多帧稳定检测数量方差: {stable_detections_variance:.2f}")
    print(f"   稳定性提升: {((single_frame_variance - stable_detections_variance) / single_frame_variance * 100):.1f}%")
    
    return {
        'single_frame_times': single_frame_times,
        'multi_frame_times': multi_frame_times,
        'single_frame_detections': single_frame_detections,
        'stable_detections': stable_detections_list
    }


def test_fusion_methods():
    """
    测试不同的掩膜融合方法
    """
    print("\n🧪 测试不同的掩膜融合方法...")
    
    test_images = create_test_image_sequence(8)
    
    fusion_methods = ["majority_vote", "confidence_weighted"]
    results = {}
    
    for method in fusion_methods:
        print(f"\n🔄 测试融合方法: {method}")
        
        detector = MultiFrameYOLODetector(
            config=default_config,
            n_frames=3,
            stable_threshold=2,
            confidence_threshold=0.3,
            fusion_method=method
        )
        
        stable_counts = []
        processing_times = []
        
        for i, image in enumerate(test_images):
            start_time = time.time()
            _, stable_detections = detector.detect_objects_multi_frame(image)
            processing_time = time.time() - start_time
            
            stable_counts.append(len(stable_detections))
            processing_times.append(processing_time)
        
        results[method] = {
            'stable_counts': stable_counts,
            'processing_times': processing_times,
            'avg_stable_count': np.mean(stable_counts),
            'avg_processing_time': np.mean(processing_times),
            'stability_variance': np.var(stable_counts)
        }
        
        print(f"   平均稳定检测数量: {results[method]['avg_stable_count']:.1f}")
        print(f"   平均处理时间: {results[method]['avg_processing_time']:.3f}s")
        print(f"   稳定性方差: {results[method]['stability_variance']:.2f}")
    
    # 对比结果
    print(f"\n📊 融合方法对比:")
    for method, result in results.items():
        print(f"   {method}:")
        print(f"     稳定检测数量: {result['avg_stable_count']:.1f}")
        print(f"     处理时间: {result['avg_processing_time']:.3f}s")
        print(f"     稳定性方差: {result['stability_variance']:.2f}")
    
    return results


def test_parameter_sensitivity():
    """
    测试参数敏感性
    """
    print("\n🧪 测试参数敏感性...")
    
    test_images = create_test_image_sequence(10)
    
    # 测试不同的稳定阈值
    stable_thresholds = [1, 2, 3, 4]
    print(f"\n🔄 测试稳定阈值: {stable_thresholds}")
    
    for threshold in stable_thresholds:
        detector = MultiFrameYOLODetector(
            config=default_config,
            n_frames=3,
            stable_threshold=threshold,
            confidence_threshold=0.3,
            fusion_method="majority_vote"
        )
        
        stable_counts = []
        for image in test_images:
            _, stable_detections = detector.detect_objects_multi_frame(image)
            stable_counts.append(len(stable_detections))
        
        print(f"   阈值 {threshold}: 平均稳定检测 {np.mean(stable_counts):.1f}, 方差 {np.var(stable_counts):.2f}")
    
    # 测试不同的融合帧数
    n_frames_list = [2, 3, 4, 5]
    print(f"\n🔄 测试融合帧数: {n_frames_list}")
    
    for n_frames in n_frames_list:
        detector = MultiFrameYOLODetector(
            config=default_config,
            n_frames=n_frames,
            stable_threshold=2,
            confidence_threshold=0.3,
            fusion_method="majority_vote"
        )
        
        processing_times = []
        stable_counts = []
        
        for image in test_images:
            start_time = time.time()
            _, stable_detections = detector.detect_objects_multi_frame(image)
            processing_time = time.time() - start_time
            
            processing_times.append(processing_time)
            stable_counts.append(len(stable_detections))
        
        print(f"   帧数 {n_frames}: 平均时间 {np.mean(processing_times):.3f}s, "
              f"平均稳定检测 {np.mean(stable_counts):.1f}")


def main():
    """主测试函数"""
    print("🚀 开始多帧检测系统测试...")
    
    try:
        # 基础功能测试
        print("\n" + "="*50)
        print("1. 基础功能测试")
        print("="*50)
        
        # 运行基础测试
        from multi_frame_yolo_detector import test_multi_frame_detector
        test_multi_frame_detector()
        
        # 性能对比测试
        print("\n" + "="*50)
        print("2. 性能对比测试")
        print("="*50)
        
        performance_results = test_single_frame_vs_multi_frame()
        
        # 融合方法测试
        print("\n" + "="*50)
        print("3. 融合方法测试")
        print("="*50)
        
        fusion_results = test_fusion_methods()
        
        # 参数敏感性测试
        print("\n" + "="*50)
        print("4. 参数敏感性测试")
        print("="*50)
        
        test_parameter_sensitivity()
        
        print("\n✅ 所有测试完成！")
        
        # 生成测试报告
        print("\n📋 测试总结:")
        print("   ✅ 多帧检测器基础功能正常")
        print("   ✅ 掩膜时序融合算法工作正常")
        print("   ✅ 物体ID跟踪功能正常")
        print("   ✅ 系统集成成功")
        
        # 性能建议
        if 'single_frame_times' in performance_results:
            avg_single = np.mean(performance_results['single_frame_times'])
            avg_multi = np.mean(performance_results['multi_frame_times'])
            overhead = (avg_multi - avg_single) / avg_single * 100
            
            if overhead < 20:
                print(f"   ✅ 性能开销可接受 ({overhead:.1f}%)")
            elif overhead < 50:
                print(f"   ⚠️ 性能开销较高 ({overhead:.1f}%)，建议优化")
            else:
                print(f"   ❌ 性能开销过高 ({overhead:.1f}%)，需要优化")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
