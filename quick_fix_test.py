#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复验证脚本
验证参数修复是否成功
"""

def test_parameter_fix():
    """测试参数修复"""
    print("🧪 测试参数修复...")
    
    try:
        from config import default_config
        from multi_frame_yolo_detector import MultiFrameYOLODetector
        
        print("📋 配置参数:")
        mf_config = default_config.multi_frame_detection
        print(f"   输入置信度阈值: {mf_config.input_confidence_threshold}")
        print(f"   输出置信度阈值: {mf_config.output_confidence_threshold}")
        print(f"   IoU匹配阈值: {mf_config.iou_match_threshold}")
        print(f"   IoU稳定性阈值: {mf_config.iou_stability_threshold}")
        
        # 测试创建多帧检测器
        print("\n🎯 创建多帧检测器...")
        detector = MultiFrameYOLODetector(config=default_config)
        
        print("✅ 多帧检测器创建成功")
        print(f"   输入置信度阈值: {detector.input_confidence_threshold}")
        print(f"   输出置信度阈值: {detector.output_confidence_threshold}")
        print(f"   IoU匹配阈值: {detector.iou_match_threshold}")
        print(f"   IoU稳定性阈值: {detector.iou_stability_threshold}")
        
        # 验证参数合理性
        assert detector.input_confidence_threshold <= 0.2, "输入阈值应该较低"
        assert detector.output_confidence_threshold >= 0.3, "输出阈值应该较高"
        assert detector.input_confidence_threshold < detector.output_confidence_threshold, "输入阈值应该低于输出阈值"
        
        print("✅ 参数验证通过")
        
        # 测试统计功能
        stats = detector.get_tracking_stats()
        print(f"✅ 统计功能正常: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_functionality():
    """测试基础功能"""
    print("\n🧪 测试基础功能...")
    
    try:
        from multi_frame_yolo_detector import TrackedObject
        from yolo_detector import Detection
        import numpy as np
        
        # 创建跟踪对象
        tracked_obj = TrackedObject(
            object_id="test_001",
            class_name="test_object",
            class_id=0
        )
        
        # 添加检测结果
        detection = Detection(
            box=np.array([100, 100, 200, 200]),
            confidence=0.2,
            class_id=0,
            class_name="test_object",
            mask=np.ones((100, 100), dtype=bool)
        )
        
        tracked_obj.add_detection(detection, 1)
        
        # 测试融合置信度计算
        fusion_conf = tracked_obj.get_fusion_confidence(1)
        print(f"   融合置信度: {fusion_conf:.3f}")
        
        # 测试稳定性判断
        is_stable = tracked_obj.is_stable_with_fusion_confidence(1, 0.3, 1)
        print(f"   稳定性判断: {is_stable}")
        
        print("✅ 基础功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 快速修复验证...")
    
    tests = [
        ("参数修复", test_parameter_fix),
        ("基础功能", test_basic_functionality)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*40}")
        print(f"测试: {test_name}")
        print('='*40)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*40}")
    print("修复验证总结")
    print('='*40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 参数修复成功！")
        print("   现在可以正常使用改进版的多帧检测系统了")
        print("   核心改进：低阈值输入(0.1)，高阈值输出(0.3)")
    else:
        print("⚠️ 部分测试失败，请检查相关实现。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*50}")
    if success:
        print("✅ 修复验证完成，系统可以正常运行")
        print("💡 建议运行: python test_improved_strategy.py")
    else:
        print("❌ 修复验证失败，需要进一步检查")
