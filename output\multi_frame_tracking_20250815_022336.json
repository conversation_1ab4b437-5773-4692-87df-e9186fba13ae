{"timestamp": "2025-08-15T02:23:36.886765", "stats": {"frame_count": 1, "total_tracked_objects": 5, "stable_objects": 0, "class_counts": {"bottle": 1, "remote": 1, "can": 1, "banana": 1, "hanger": 1}, "stable_class_counts": {}, "fusion_method": "majority_vote", "n_frames": 3, "stable_threshold": 2}, "tracked_objects": {"716d5282": {"class_name": "bottle", "class_id": 0, "consecutive_frames": 1, "is_stable": false, "last_seen_frame": 0, "detection_count": 1}, "27a662fd": {"class_name": "remote", "class_id": 5, "consecutive_frames": 1, "is_stable": false, "last_seen_frame": 0, "detection_count": 1}, "907f64c1": {"class_name": "can", "class_id": 10, "consecutive_frames": 1, "is_stable": false, "last_seen_frame": 0, "detection_count": 1}, "21b536ce": {"class_name": "banana", "class_id": 1, "consecutive_frames": 1, "is_stable": false, "last_seen_frame": 0, "detection_count": 1}, "e44842f2": {"class_name": "hanger", "class_id": 13, "consecutive_frames": 1, "is_stable": false, "last_seen_frame": 0, "detection_count": 1}}}