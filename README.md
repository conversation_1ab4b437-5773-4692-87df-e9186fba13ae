# YOLO点云联合检测系统

一个结合YOLO物体检测和点云分析的智能检测系统，能够区分真实物体和平面图片。

## 系统概述

本系统通过以下步骤实现智能物体检测：

1. **YOLO物体检测**: 使用YOLOv8模型检测图像中的物体并获取边界框
2. **坐标转换**: 将边界框内的像素坐标转换为3D点云坐标
3. **双重平面检测**: 自动检测地面和桌面，建立准确的桌面坐标系
4. **桌面高度过滤**: 基于物体在桌面坐标系下的平均高度过滤非桌面物体
5. **高度分析**: 分析物体在桌面坐标系中的高度分布
6. **物体分类**: 基于高度方差判断物体是真实的还是平面图片

## 系统架构

```
YOLO点云联合检测/
├── config.py              # 系统配置管理
├── yolo_detector.py        # YOLO物体检测模块
├── coordinate_transformer.py # 坐标转换模块（已存在）
├── object_analyzer.py      # 物体分析模块
├── main.py                # 主程序
├── test_system.py         # 系统测试脚本
├── yolov8n.pt            # YOLO模型文件
└── README.md             # 本文件
```

## 核心功能

### 1. 物体检测 (`yolo_detector.py`)
- 基于YOLOv8的实时物体检测
- 支持80种COCO数据集物体类别
- 可配置的置信度和IoU阈值
- 检测结果可视化和保存

### 2. 坐标转换 (`coordinate_transformer.py`)
- 像素坐标到3D点云坐标转换
- 双重RANSAC平面检测（地面+桌面）
- 相机坐标系到桌面坐标系转换
- 基于桌面高度的物体过滤
- 支持多种相机内参配置

### 3. 物体分析 (`object_analyzer.py`)
- 基于高度方差的物体分类
- 真实物体vs平面物体判断
- 3D点云采样和分析
- 结果可视化和统计

### 4. 系统集成 (`main.py`)
- 完整的检测流水线
- 实时图像处理和分析
- 结果保存和可视化
- 交互式操作界面

## 安装要求

### 必需依赖
```bash
pip install ultralytics  # YOLOv8
pip install opencv-python  # OpenCV
pip install numpy  # 数值计算
pip install matplotlib  # 可视化
pip install scikit-learn  # 机器学习算法
pip install pyorbbecsdk  # 相机SDK（如果使用奥比中光相机）
```

### 硬件要求
- 支持深度信息的相机（如Intel RealSense、奥比中光等）
- 或者已有的深度图像数据

## 配置说明

系统配置在 `config.py` 中定义：

### YOLO配置
```python
class YOLOConfig:
    model_path: str = "yolov8n.pt"  # 模型路径
    confidence_threshold: float = 0.5  # 置信度阈值
    iou_threshold: float = 0.5  # IoU阈值
    device: str = "auto"  # 设备选择
    target_classes: List[int] = None  # 目标类别
```

### 物体分析配置
```python
class ObjectAnalysisConfig:
    variance_threshold: float = 100.0  # 方差阈值 (mm²)
    min_points_threshold: int = 50  # 最小点数
    real_object_min_variance: float = 50.0  # 真实物体最小方差 (mm²)
    flat_object_max_variance: float = 25.0  # 平面物体最大方差 (mm²)
    table_height_threshold_mm: float = 50.0  # 桌面高度阈值（毫米）
    table_height_filter_min_points: int = 20  # 高度过滤最小点数
```

## 使用方法

### 1. 基础测试
首先运行测试脚本验证系统安装：
```bash
python test_system.py
```

### 2. 完整系统运行
```bash
python main.py
```

### 3. 操作选项
系统提供以下操作模式：
- **单帧检测**: 处理单张图像
- **连续检测**: 实时连续处理
- **自定义检测**: 设置帧数和保存间隔

### 4. 控制键
- `q`: 退出检测
- `s`: 手动保存当前结果

## 输出结果

### 文件输出
系统会在 `输出/` 目录下保存：
- `frame_*.jpg`: 标注后的图像
- `frame_*.json`: 检测和分析结果
- `session_*_analysis.json`: 会话分析结果
- `session_*_visualization.png`: 结果可视化图表
- `session_*_summary.json`: 会话摘要

### 结果解读
每个检测物体会显示：
- **绿色边框**: 真实物体
- **红色边框**: 平面物体
- **方差值**: 高度分布方差
- **置信度**: 分类置信度

## 算法原理

### 真实物体判断标准
1. **高度方差**: 真实物体在桌面坐标系中有高度变化
2. **点云密度**: 足够的有效3D点
3. **空间分布**: 合理的空间分布模式

### 阈值设置
- 方差 > 0.005: 很可能是真实物体
- 方差 < 0.0005: 很可能是平面物体
- 0.0005 ≤ 方差 ≤ 0.005: 需要综合判断

## 故障排除

### 常见问题

1. **相机连接失败**
   - 检查相机是否正确连接
   - 确认相机驱动已安装
   - 检查USB端口和线缆

2. **平面检测失败**
   - 确保相机能看到足够大的平面
   - 调整相机角度和距离
   - 检查平面是否有足够的纹理

3. **YOLO模型加载失败**
   - 确认 `yolov8n.pt` 文件存在
   - 检查网络连接（首次使用会下载）
   - 检查磁盘空间

4. **检测精度不高**
   - 调整YOLO置信度阈值
   - 修改物体分析的方差阈值
   - 改善光照条件

### 参数调优

根据实际使用情况调整以下参数：

```python
# 提高检测精度
config.update_config({
    'yolo': {
        'confidence_threshold': 0.7
    }
})

# 调整物体分析敏感度
config.update_config({
    'object_analysis': {
        'variance_threshold': 0.002,
        'real_object_min_variance': 0.008
    }
})
```

## 扩展功能

### 添加新的物体类别
修改 `config.py` 中的 `target_classes`：
```python
config.update_config({
    'yolo': {
        'target_classes': [0, 1, 2]  # 只检测人、自行车、汽车
    }
})
```

### 自定义分析算法
在 `object_analyzer.py` 中扩展 `_classify_object` 方法。

### 集成其他传感器
在 `coordinate_transformer.py` 中添加新的传感器支持。

## 技术支持

- 检查日志输出获取详细错误信息
- 使用测试脚本验证各模块功能
- 参考各模块的文档字符串获取API说明

## 版本信息

- 当前版本: 1.0.0
- 支持Python: 3.7+
- 主要依赖: YOLOv8, OpenCV, NumPy, Matplotlib

---

**注意**: 首次运行时，YOLOv8会自动下载预训练模型，请确保网络连接正常。 