#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结果显示模块
提供检测和分析结果的展示和统计功能
"""

from typing import List, Tuple
from yolo_detector import Detection
from object_analyzer import ObjectAnalysisResult
import cv2

class ResultDisplay:
    """结果显示器"""
    
    @staticmethod
    def display_results(detections: List[Detection], analysis_results: List[ObjectAnalysisResult]):
        """
        在控制台中显示检测和分析结果
        
        Args:
            detections: YOLO检测结果
            analysis_results: 物体分析结果
        """
        print("\n" + "="*30)
        print("📊  分析结果摘要")
        print("="*30)
        
        if not detections:
            print("  -> 未检测到任何物体")
            return
            
        if not analysis_results:
            print("  -> 所有检测到的物体都未能成功分析 (可能点云数量不足)")
            return
        
        # 创建一个从 detection index 到 analysis result 的映射
        analysis_map = {}
        for res in analysis_results:
            if hasattr(res, 'detection_index'):
                analysis_map[res.detection_index] = res
        
        for i, detection in enumerate(detections):
            print(f"\n--- 物体 {i+1}: {detection.class_name} (置信度: {detection.confidence:.2f}) ---")
            
            # 查找对应的分析结果
            result = analysis_map.get(i)
            
            if result:
                if hasattr(result, 'classification') and result.classification == 'failed':
                    status = f"❌ 分析失败"
                    print(f"  状态: {status}")
                else:
                    # 适应不同的属性名称
                    is_real = result.is_real_object if hasattr(result, 'is_real_object') else False
                    confidence = result.confidence if hasattr(result, 'confidence') else 0.0
                    point_count = result.point_count if hasattr(result, 'point_count') else 0
                    height_variance = result.height_variance if hasattr(result, 'height_variance') else 0.0
                    
                    status = "✅ 真实物体" if is_real else "⚪️ 平面物体"
                    print(f"  分类: {status} (置信度: {confidence:.2f})")
                    print(f"  点云统计: {point_count} 个点")
                    print(f"  高度方差: {height_variance:.3f} mm²")
            else:
                print("  -> ⚠️ 未能分析此物体 (可能被过滤)")

        print("\n" + "="*30)
    
    @staticmethod
    def display_processing_complete():
        """显示处理完成信息"""
        print("✅ Frame processing complete!")
        print("   Press P to view point cloud")
        print("   Press 3 to view interactive 3D point cloud with bounding boxes")
        print("   Press any other key to continue...")
    
    @staticmethod
    def display_interactive_mode_instructions():
        """显示交互模式说明"""
        print("📷 Interactive Frame Capture Mode")
        print("   Press SPACE to capture frame for processing")
        print("   Press Q to quit")
        print("   Press S to save current frame")
    
    @staticmethod
    def display_pointcloud_instructions():
        """显示点云查看说明"""
        print("🔹 Point Cloud Controls:")
        print("  - Left mouse drag: Rotate view")
        print("  - Right mouse drag: Pan")
        print("  - Mouse wheel: Zoom")
        print("  - Press H: Show help")
    
    @staticmethod
    def display_3d_pointcloud_instructions():
        """显示3D点云交互说明"""
        print("🔹 交互控制:")
        print("   - 鼠标左键拖拽: 旋转视角")
        print("   - 鼠标右键拖拽: 平移视角")
        print("   - 鼠标滚轮: 缩放")
        print("   - 按 H 键: 显示帮助")
        print("   - 按 R 键: 重置视角")
        print("   - 按 ESC 键: 关闭窗口")
    
    @staticmethod
    def display_system_status(system):
        """显示系统状态"""
        print(f"📈 System Statistics:")
        print(f"   Frames processed: {system.frame_count}")
        print(f"   Objects detected: {system.detection_count}")
        print(f"   Objects analyzed: {system.analysis_count}")
        if system.frame_count > 0:
            print(f"   Average detections per frame: {system.detection_count / system.frame_count:.1f}")
            print(f"   Average analysis per frame: {system.analysis_count / system.frame_count:.1f}")
    
    @staticmethod
    def display_error_message(error_msg: str, error_type: str = "Error"):
        """显示错误信息"""
        print(f"❌ {error_type}: {error_msg}")
    
    @staticmethod
    def display_success_message(message: str):
        """显示成功信息"""
        print(f"✅ {message}")
    
    @staticmethod
    def display_warning_message(message: str):
        """显示警告信息"""
        print(f"⚠️ {message}")
    
    @staticmethod
    def display_info_message(message: str):
        """显示信息消息"""
        print(f"ℹ️ {message}") 