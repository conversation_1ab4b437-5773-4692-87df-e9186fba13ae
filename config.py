#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
包含所有系统配置参数和路径设置
"""

import os
from dataclasses import dataclass
from typing import Dict, Any


@dataclass
class CameraConfig:
    """相机配置参数"""
    depth_width: int = 640
    depth_height: int = 480
    color_width: int = 640
    color_height: int = 480


@dataclass
class YOLOConfig:
    """YOLO检测配置参数"""
    model_path: str = "train_8.pt"
    confidence_threshold: float = 0.1
    iou_threshold: float = 0.8
    device: str = "cuda"  # 可选: "cpu", "cuda", "mps"

    # 要检测的类别（自定义数据集类别ID）
    target_classes: list = None  # None表示检测所有类别

    def __post_init__(self):
        if self.target_classes is None:
            # 使用自定义数据集的所有类别
            self.target_classes = list(range(14))  # 0-13，共14个类别


@dataclass
class MultiFrameDetectionConfig:
    """多帧检测配置参数"""
    enabled: bool = True  # 是否启用多帧检测
    n_frames: int = 3  # 用于融合的帧数（建议3-5）
    stable_threshold: int = 2  # 稳定物体阈值（连续出现≥K帧才计入最终数量，建议2-3）
    confidence_threshold: float = 0.15  # 置信度阈值（降低到0.15，让更多候选进入多帧处理）
    iou_threshold: float = 0.5  # IoU阈值（用于物体匹配，建议0.5）
    fusion_method: str = "majority_vote"  # 融合方法（"majority_vote" 或 "confidence_weighted"）
    max_missing_frames: int = 5  # 物体消失超过此帧数后从跟踪列表中移除

    # 稳定性判断参数
    min_stable_confidence: float = 0.3  # 稳定物体的最低平均置信度
    iou_stability_threshold: float = 0.3  # IoU稳定性阈值，低于此值重置稳定计数
    enable_iou_stability_check: bool = True  # 是否启用IoU稳定性检查

    # 匹配分数权重配置
    match_weights: dict = None

    def __post_init__(self):
        if self.match_weights is None:
            self.match_weights = {
                'iou': 0.5,
                'confidence': 0.2,
                'time_decay': 0.15,
                'stability': 0.05,
                'mask': 0.1
            }

@dataclass
class ImagePreprocessingConfig:
    """图像预处理配置参数"""
    # 直方图均衡化
    histogram_equalization_enabled: bool = True
    histogram_equalization_method: str = "clahe" # 添加的参数 或equalize
    # --- 添加以下两个 CLAHE 相关参数 ---
    clahe_clip_limit: float = 2.0
    clahe_tile_grid_size: tuple = (8, 8) # 注意：dataclass 中默认值为 tuple
    # ----------------------------------

    # 超分辨率
    super_resolution_enabled: bool = True
    super_resolution_method: str = "bicubic" # "bicubic", "lanczos", "inter_cubic", "inter_linear", "inter_nearest"
    target_width: int = 1920
    target_height: int = 1440
    scale_factor: float = 3.0 # 640*480 -> 1920*1440 = 3倍

    # 降噪处理
    noise_reduction_enabled: bool = False
    noise_reduction_method: str = "bilateral" # 添加的参数
    # --- 添加以下降噪相关参数 ---
    # Bilateral Filter
    bilateral_d: int = 9
    bilateral_sigma_color: float = 75.0
    bilateral_sigma_space: float = 75.0
    # Gaussian Blur
    gaussian_ksize: tuple = (5, 5) # 注意：dataclass 中默认值为 tuple
    gaussian_sigma_x: float = 0.0
    # Median Blur
    median_ksize: int = 5
    # -----------------------------

    # 锐化处理
    sharpening_enabled: bool = False
    sharpening_method: str = "unsharp_mask" # 添加的参数
    # --- 添加以下锐化相关参数 ---
    sharpening_kernel_size: tuple = (5, 5) # 注意：dataclass 中默认值为 tuple
    sharpening_sigma: float = 1.0
    sharpening_amount: float = 1.5
    sharpening_threshold: float = 0.0
    # -----------------------------


@dataclass
class PlaneDetectionConfig:
    """平面检测配置参数"""
    # 第一次RANSAC检测地面的参数 (更宽松的距离阈值，保持高内点比例要求)
    first_ransac_min_samples: int = 3
    first_ransac_residual_threshold: float = 20.0  # 毫米
    first_ransac_max_trials: int = 3000
    first_ransac_min_inliers_ratio: float = 0.3     # 保持 30%，确保检测到显著平面
    
    # 第二次RANSAC检测桌面的参数 (保持或稍放宽距离阈值，显著降低内点比例要求)
    second_ransac_min_samples: int = 3
    second_ransac_residual_threshold: float = 10.0  # 毫米 - 可与第一次相同或稍小
    second_ransac_max_trials: int = 3000
    second_ransac_min_inliers_ratio: float = 0.1    # 从 0.3 降低到 0.1 (10%)，适应较小的桌面
    

@dataclass
class ObjectAnalysisConfig:
    """物体分析配置参数"""
    # 最小点数阈值，用于有效分析
    min_points_threshold: int = 50

    # 高度分析参数
    max_height_threshold: float = 1000.0  # 最大合理物体高度（毫米），增加到1000mm
    min_height_threshold: float = 1.0  # 最小物体高度（毫米）

    # 采样参数
    max_analysis_points: int = 5000  # 用于分析的最大点数

    # 基于物体平均高度的方差分类阈值
    variance_classification_threshold: float = 5.0  # 方差分类阈值 (mm²)，基于物体平均高度计算，大于此值为立体物体
    
    # 高度范围分类阈值
    height_range_classification_threshold: float = 12.0  # 高度范围阈值 (mm)，大于此值判定为立体物体
    
    # 掩膜处理参数
    use_mask_erosion: bool = True  # 是否启用掩膜腐蚀处理
    mask_erosion_size: int = 2  # 掩膜腐蚀核大小（从2减少到1，减少偏移）
    mask_erosion_iterations: int = 2  # 腐蚀迭代次数


@dataclass
class TextRecognitionConfig:
    """文字识别配置参数"""
    # 是否启用文字识别功能
    enabled: bool = True
    
    # PaddleOCR模型配置 - 使用对中文支持更好的v4版本
    detection_model: str = "PP-OCRv4_server_det"  # 检测模型名称
    recognition_model: str = "PP-OCRv4_server_rec"  # 识别模型名称
    
    # 仿射变换标定文件路径（相对路径，将在运行时转换为绝对路径）
    calibration_file: str = "affine_calibration_results.json"
    
    # 文字识别阈值 - 降低阈值以提高中文识别率
    confidence_threshold: float = 0.2  # 文字识别置信度阈值
    
    # ROI处理参数
    roi_padding: int = 15  # ROI边界扩展像素数
    min_roi_width: int = 10  # 最小ROI宽度
    min_roi_height: int = 10  # 最小ROI高度
    
    # 图像预处理参数
    enable_preprocessing: bool = True  # 是否启用OCR预处理
    adaptive_threshold_block_size: int = 11  # 自适应阈值块大小
    adaptive_threshold_c: int = 2  # 自适应阈值常数
    
    # 输出配置
    save_text_results: bool = True  # 是否保存文字识别结果
    temp_dir: str = "temp_text_regions"  # 临时文件目录
    
    def get_calibration_file_path(self) -> str:
        """获取标定文件的完整路径"""
        return os.path.join(PathConfig.MODEL_DIR, self.calibration_file)


# ==============================================================================
# 路径配置
# ==============================================================================
class PathConfig:
    """
    统一管理项目中的所有路径，确保输出文件整洁有序。
    所有输出都将被重定向到项目根目录下的 'output' 文件夹中。
    """
    # 项目根目录 (YOLO点云联合检测)
    PROJECT_DIR = os.path.dirname(os.path.abspath(__file__))

    # 主输出目录
    OUTPUT_DIR = os.path.join(PROJECT_DIR, "output")

    # 子目录
    DATA_DIR = os.path.join(OUTPUT_DIR, "数据")  # 用于保存图像、点云、分析结果等
    LOG_DIR = os.path.join(OUTPUT_DIR, "logs")   # 用于保存日志文件
    MODEL_DIR = os.path.join(OUTPUT_DIR, "models") # 用于保存下载的YOLO模型

    @classmethod
    def setup_directories(cls):
        """
        在程序启动时创建所有必要的输出目录。
        """
        print("📁 正在设置所有输出目录...")
        os.makedirs(cls.OUTPUT_DIR, exist_ok=True)
        os.makedirs(cls.DATA_DIR, exist_ok=True)
        os.makedirs(cls.LOG_DIR, exist_ok=True)
        os.makedirs(cls.MODEL_DIR, exist_ok=True)
        print(f"   - 主输出目录: {cls.OUTPUT_DIR}")
        print(f"   - 数据目录: {cls.DATA_DIR}")
        print(f"   - 日志目录: {cls.LOG_DIR}")
        print(f"   - 模型目录: {cls.MODEL_DIR}")
        print("✅ 所有目录设置完成。")


# ==============================================================================
# 系统总配置
# ==============================================================================
class SystemConfig:
    """系统总配置类"""
    
    def __init__(self):
        self.camera = CameraConfig()
        self.yolo = YOLOConfig()
        self.multi_frame_detection = MultiFrameDetectionConfig()
        self.image_preprocessing = ImagePreprocessingConfig()
        self.plane_detection = PlaneDetectionConfig()
        self.object_analysis = ObjectAnalysisConfig()
        self.text_recognition = TextRecognitionConfig()
        self.paths = PathConfig()
    
    def print_config(self):
        """打印当前配置"""
        print("📋 系统配置:")
        print(f"  📷 相机配置:")
        print(f"     深度分辨率: {self.camera.depth_width}x{self.camera.depth_height}")
        print(f"     彩色分辨率: {self.camera.color_width}x{self.camera.color_height}")
        
        print(f"  🎯 YOLO配置:")
        print(f"     模型路径: {self.yolo.model_path}")
        print(f"     置信度阈值: {self.yolo.confidence_threshold}")
        print(f"     IoU阈值: {self.yolo.iou_threshold}")
        print(f"     设备: {self.yolo.device}")
        print(f"     目标类别数: {len(self.yolo.target_classes) if self.yolo.target_classes else '全部'}")

        print(f"  🎯 多帧检测配置:")
        print(f"     功能状态: {'启用' if self.multi_frame_detection.enabled else '禁用'}")
        if self.multi_frame_detection.enabled:
            print(f"     融合帧数: {self.multi_frame_detection.n_frames}")
            print(f"     稳定阈值: {self.multi_frame_detection.stable_threshold} 帧")
            print(f"     初始置信度阈值: {self.multi_frame_detection.confidence_threshold} (宽松过滤)")
            print(f"     稳定物体最低置信度: {self.multi_frame_detection.min_stable_confidence}")
            print(f"     IoU阈值: {self.multi_frame_detection.iou_threshold}")
            print(f"     IoU稳定性阈值: {self.multi_frame_detection.iou_stability_threshold}")
            print(f"     IoU稳定性检查: {'启用' if self.multi_frame_detection.enable_iou_stability_check else '禁用'}")
            print(f"     融合方法: {self.multi_frame_detection.fusion_method}")
            print(f"     最大丢失帧数: {self.multi_frame_detection.max_missing_frames}")
        
        print(f"  🖼️  图像预处理配置:")
        print(f"     直方图均衡化: {'启用' if self.image_preprocessing.histogram_equalization_enabled else '禁用'}")
        print(f"     超分辨率: {'启用' if self.image_preprocessing.super_resolution_enabled else '禁用'}")
        if self.image_preprocessing.super_resolution_enabled:
            print(f"     目标分辨率: {self.image_preprocessing.target_width}x{self.image_preprocessing.target_height}")
            print(f"     缩放因子: {self.image_preprocessing.scale_factor}")
        print(f"     降噪处理: {'启用' if self.image_preprocessing.noise_reduction_enabled else '禁用'}")
        print(f"     锐化处理: {'启用' if self.image_preprocessing.sharpening_enabled else '禁用'}")
        
        print(f"  📐 平面检测配置:")
        print(f"     第一次RANSAC(地面):")
        print(f"       距离阈值: {self.plane_detection.first_ransac_residual_threshold}mm")
        print(f"       最大迭代: {self.plane_detection.first_ransac_max_trials}")
        print(f"       最小内点比例: {self.plane_detection.first_ransac_min_inliers_ratio}")
        print(f"     第二次RANSAC(桌面):")
        print(f"       距离阈值: {self.plane_detection.second_ransac_residual_threshold}mm")
        print(f"       最大迭代: {self.plane_detection.second_ransac_max_trials}")
        print(f"       最小内点比例: {self.plane_detection.second_ransac_min_inliers_ratio}")
        
        print(f"  🔍 物体分析配置:")
        print(f"     最小点数: {self.object_analysis.min_points_threshold}")
        print(f"     高度范围: {self.object_analysis.min_height_threshold}~{self.object_analysis.max_height_threshold}mm")
        print(f"     方差分类阈值: {self.object_analysis.variance_classification_threshold:.1f}mm² (基于物体平均高度，大于此值为立体物体)")
        print(f"     高度范围阈值: {self.object_analysis.height_range_classification_threshold:.1f}mm (基于物体高度范围，大于此值为立体物体)")
        
        print(f"  📝 文字识别配置:")
        print(f"     功能状态: {'启用' if self.text_recognition.enabled else '禁用'}")
        print(f"     检测模型: {self.text_recognition.detection_model}")
        print(f"     识别模型: {self.text_recognition.recognition_model}")
        print(f"     标定文件: {self.text_recognition.calibration_file}")
        print(f"     置信度阈值: {self.text_recognition.confidence_threshold}")
        print(f"     ROI边界扩展: {self.text_recognition.roi_padding}px")
        print(f"     预处理: {'启用' if self.text_recognition.enable_preprocessing else '禁用'}")
        
        print(f"  📂 路径配置:")
        print(f"     数据目录: {self.paths.DATA_DIR}")
        print(f"     输出目录: {self.paths.OUTPUT_DIR}")
        print(f"     日志目录: {self.paths.LOG_DIR}")


# 自定义数据集类别名称（train_6.pt模型，14类）
CUSTOM_CLASS_NAMES = {
    0: 'bottle',
    1: 'banana',
    2: 'orange',
    3: 'dining table',
    4: 'mouse',
    5: 'remote',
    6: 'cell phone',
    7: 'book',
    8: 'scissors',
    9: 'toothbrush',
    10: 'can',
    11: 'box',
    12: 'jelly',
    13: 'hanger'
}


def get_class_name(class_id: int) -> str:
    """获取类别名称"""
    return CUSTOM_CLASS_NAMES.get(class_id, f"unknown_{class_id}")


# 创建默认配置实例
default_config = SystemConfig()


if __name__ == "__main__":
    # 测试配置模块
    config = SystemConfig()
    config.print_config()
    
    print("✅ 配置更新完成")