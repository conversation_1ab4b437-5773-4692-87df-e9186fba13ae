# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a computer vision system that combines **YOLO object detection** with **3D point cloud analysis** to distinguish between real physical objects and flat images/pictures. Built for Orbbec depth camera integration, it processes depth and color frames to classify detected objects as either "real" (3D objects) or "flat" (images/pictures) based on height variance analysis.

## Architecture

The system follows a **modular pipeline architecture**:

### Core Components

1. **Main System** (`main.py`) - `YOLOPointCloudSystem` class orchestrates the detection pipeline with interactive controls (spacebar to capture, 'q' to quit, 's' to save)

2. **Configuration** (`config.py`) - Centralized `SystemConfig` with subsections:
   - `CameraConfig`: Resolution (640×480 default), frame timeout
   - `YOLOConfig`: Model path (yolov8m.pt), confidence thresholds (0.5)
   - `ObjectAnalysisConfig`: Height variance thresholds for classification

3. **Detection Pipeline**:
   - **YOLO Detection** (`yolo_detector.py`) - YOLOv8 with CUDA/CPU support
   - **Coordinate Transform** (`coordinate_transformer.py`) - 2D pixels → 3D point cloud → desktop coordinates
   - **Object Analysis** (`object_analyzer.py`) - Classifies objects based on Z-axis height variance

4. **Visualization**:
   - **3D Display** (`pointcloud_visualizer.py`) - Open3D interactive point cloud viewer
   - **Results** (`result_display.py`) - Console output with detection statistics

### Data Flow

```
Camera Frames → YOLO Detection → Coordinate Transform → Plane Detection → Height Analysis → Classification
```

## Key Dependencies

- `ultralytics>=8.0.0` - YOLOv8 object detection
- `pyorbbecsdk` - Orbbec depth camera interface
- `open3d` - 3D point cloud visualization
- `opencv-python`, `numpy`, `scikit-learn` - Core processing

## Development Commands

```bash
# Run main system
python main.py

# Install dependencies
pip install -r requirements.txt

# Test individual modules
python yolo_detector.py
python object_analyzer.py
```

## Configuration Parameters

**Object Classification Thresholds** (in `config.py`):
- `variance_threshold`: 100.0 mm² (general classification)
- `real_object_min_variance`: 50.0 mm² (minimum for real objects)
- `flat_object_max_variance`: 25.0 mm² (maximum for flat objects)

**YOLO Settings**:
- Model: `output/models/yolov8m.pt` (or yolov8m-seg.pt for segmentation)
- Confidence: 0.5, IoU: 0.45

## Output Structure

All outputs saved to `output/` directory:
- `数据/res_frame_XXXX.jpg` - Annotated detection images
- `数据/analysis_frame_XXXX.json` - Detailed analysis data
- `logs/` - System logs
- `models/` - YOLO model files

## Interactive Controls

- **Spacebar**: Capture and analyze current frame
- **Q**: Quit application
- **S**: Save current frame
- **P**: Display 3D point cloud visualization