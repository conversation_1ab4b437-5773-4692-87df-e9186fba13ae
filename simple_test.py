#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的多帧检测系统测试
"""

import numpy as np
import sys
import os

def test_imports():
    """测试模块导入"""
    print("🧪 测试模块导入...")
    
    try:
        from config import default_config
        print("✅ config 模块导入成功")
        
        from multi_frame_yolo_detector import MultiFrameYOLODetector
        print("✅ multi_frame_yolo_detector 模块导入成功")
        
        from yolo_detector import YOLODetector
        print("✅ yolo_detector 模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config():
    """测试配置"""
    print("\n🧪 测试配置...")
    
    try:
        from config import default_config
        
        print("📋 系统配置:")
        print(f"   YOLO模型: {default_config.yolo.model_path}")
        print(f"   YOLO置信度阈值: {default_config.yolo.confidence_threshold}")
        print(f"   多帧检测启用: {default_config.multi_frame_detection.enabled}")
        print(f"   融合帧数: {default_config.multi_frame_detection.n_frames}")
        print(f"   稳定阈值: {default_config.multi_frame_detection.stable_threshold}")
        print(f"   融合方法: {default_config.multi_frame_detection.fusion_method}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_frame_detector_creation():
    """测试多帧检测器创建"""
    print("\n🧪 测试多帧检测器创建...")
    
    try:
        from config import default_config
        from multi_frame_yolo_detector import MultiFrameYOLODetector
        
        # 创建多帧检测器（不加载YOLO模型）
        detector = MultiFrameYOLODetector(
            config=default_config,
            n_frames=3,
            stable_threshold=2,
            confidence_threshold=0.5,
            fusion_method="majority_vote"
        )
        
        print("✅ 多帧检测器创建成功")
        
        # 测试基本属性
        print(f"   融合帧数: {detector.n_frames}")
        print(f"   稳定阈值: {detector.stable_threshold}")
        print(f"   置信度阈值: {detector.confidence_threshold}")
        print(f"   IoU阈值: {detector.iou_threshold}")
        print(f"   融合方法: {detector.fusion_method}")
        
        # 测试统计功能
        stats = detector.get_tracking_stats()
        print(f"   初始统计: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ 多帧检测器创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tracked_object():
    """测试跟踪对象"""
    print("\n🧪 测试跟踪对象...")
    
    try:
        from multi_frame_yolo_detector import TrackedObject
        from yolo_detector import Detection
        
        # 创建跟踪对象
        tracked_obj = TrackedObject(
            object_id="test_001",
            class_name="test_object",
            class_id=0
        )
        
        print("✅ 跟踪对象创建成功")
        
        # 创建模拟检测结果
        detection = Detection(
            box=np.array([100, 100, 200, 200]),
            confidence=0.8,
            class_id=0,
            class_name="test_object",
            mask=np.ones((100, 100), dtype=bool)
        )
        
        # 添加检测结果
        tracked_obj.add_detection(detection, 1, detection.mask)
        
        print(f"   检测历史长度: {len(tracked_obj.detections_history)}")
        print(f"   掩膜历史长度: {len(tracked_obj.masks_history)}")
        print(f"   连续帧数: {tracked_obj.consecutive_frames}")
        
        # 测试掩膜融合
        fused_mask = tracked_obj.get_fused_mask("majority_vote", 1)
        if fused_mask is not None:
            print(f"   融合掩膜形状: {fused_mask.shape}")
            print("✅ 掩膜融合功能正常")
        else:
            print("⚠️ 掩膜融合返回None")
        
        return True
        
    except Exception as e:
        print(f"❌ 跟踪对象测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_system_integration():
    """测试主系统集成"""
    print("\n🧪 测试主系统集成...")
    
    try:
        # 只测试导入和初始化，不运行实际检测
        from main import YOLOPointCloudSystem
        from config import default_config
        
        # 创建系统实例
        system = YOLOPointCloudSystem(default_config)
        
        print("✅ 主系统创建成功")
        print(f"   多帧检测启用: {system.use_multi_frame_detection}")
        
        # 检查组件是否正确初始化
        if hasattr(system, 'multi_frame_detector'):
            print("✅ 多帧检测器属性存在")
        else:
            print("⚠️ 多帧检测器属性不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 主系统集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始简化测试...")
    
    tests = [
        ("模块导入", test_imports),
        ("配置测试", test_config),
        ("多帧检测器创建", test_multi_frame_detector_creation),
        ("跟踪对象", test_tracked_object),
        ("主系统集成", test_main_system_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*50}")
    print("测试总结")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！多帧检测系统基础功能正常。")
        
        print("\n💡 使用建议:")
        print("   1. 多帧检测已集成到主系统中")
        print("   2. 可通过config.py调整多帧检测参数")
        print("   3. 在相机预览模式中按 'm' 查看跟踪状态")
        print("   4. 在相机预览模式中按 'r' 重置跟踪状态")
        print("   5. 稳定的检测结果会自动传递给深度分析器")
        
        print("\n🎯 核心功能:")
        print("   ✅ 连续多帧YOLO检测")
        print("   ✅ 掩膜时序融合（多数投票 + 置信度加权）")
        print("   ✅ 稳定ID跟踪（IoU + 类别匹配）")
        print("   ✅ 数量稳定输出（连续K帧才计入）")
        print("   ✅ 向分析器传递稳定结果")
        
    else:
        print("⚠️ 部分测试失败，请检查相关模块。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
