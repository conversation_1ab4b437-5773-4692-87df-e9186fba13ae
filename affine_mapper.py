#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仿射变换映射模块
用于将640×480分辨率的检测结果映射到1920×1080分辨率
"""

import cv2
import numpy as np
import json
import os
from typing import List, Tuple, Optional, Union
from dataclasses import dataclass

@dataclass
class MappedRegion:
    """映射后的区域信息"""
    bbox: Tuple[int, int, int, int]  # (x1, y1, x2, y2)
    mask: Optional[np.ndarray] = None
    confidence: float = 0.0
    class_name: str = ""
    original_bbox: Tuple[int, int, int, int] = None


class AffineMapper:
    """
    仿射变换映射器
    负责将640×480分辨率的检测结果映射到1920×1080分辨率
    """
    
    def __init__(self, calibration_file: str = "../output/models/affine_calibration_results.json"):
        """
        初始化仿射变换映射器
        
        Args:
            calibration_file: 标定结果文件路径
        """
        self.calibration_file = calibration_file
        self.affine_matrix_640_to_1920 = None
        self.affine_matrix_1920_to_640 = None
        self.calibration_info = None
        
        self.load_calibration_results()
        
        print("🔄 仿射变换映射器初始化完成")
    
    def load_calibration_results(self) -> bool:
        """
        加载标定结果
        
        Returns:
            是否成功加载标定结果
        """
        try:
            if not os.path.exists(self.calibration_file):
                print(f"❌ 未找到标定文件: {self.calibration_file}")
                return False
                
            with open(self.calibration_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            # 提取变换矩阵
            self.affine_matrix_640_to_1920 = np.array(results['affine_transform']['matrix_640_to_1920'])
            self.affine_matrix_1920_to_640 = np.array(results['affine_transform']['matrix_1920_to_640'])
            self.calibration_info = results['calibration_info']
            
            print(f"✅ 成功加载仿射变换标定结果:")
            print(f"   标定板: {self.calibration_info['board_size']} 内角点")
            print(f"   有效图像对: {self.calibration_info['valid_image_pairs']}")
            print(f"   平均误差: {results['accuracy_evaluation']['mean_error']:.2f} 像素")
            
            return True
            
        except FileNotFoundError:
            print(f"❌ 未找到标定文件: {self.calibration_file}")
            return False
        except Exception as e:
            print(f"❌ 加载标定结果失败: {e}")
            return False
    
    def transform_point_640_to_1920(self, x: float, y: float) -> Tuple[float, float]:
        """
        将640×480分辨率下的点坐标转换为1920×1080分辨率

        Args:
            x, y: 640×480分辨率下的坐标

        Returns:
            1920×1080分辨率下的坐标
        """
        if self.affine_matrix_640_to_1920 is None:
            raise ValueError("未加载标定结果")

        # 使用cv2.transform进行正确的仿射变换
        # 将点转换为正确的格式：(1, 1, 2) 表示1个点，每个点有2个坐标
        point_640 = np.array([[[x, y]]], dtype=np.float32)

        # 应用仿射变换
        point_1920 = cv2.transform(point_640, self.affine_matrix_640_to_1920)

        # 提取结果
        x_1920, y_1920 = point_1920[0, 0]

        return float(x_1920), float(y_1920)
    
    def transform_bbox_640_to_1920(self, bbox: Tuple[int, int, int, int]) -> Tuple[int, int, int, int]:
        """
        将640×480分辨率下的边界框转换为1920×1080分辨率
        
        Args:
            bbox: (x1, y1, x2, y2) 640×480分辨率下的边界框
            
        Returns:
            1920×1080分辨率下的边界框
        """
        x1, y1, x2, y2 = bbox
        
        # 转换四个角点
        x1_1920, y1_1920 = self.transform_point_640_to_1920(x1, y1)
        x2_1920, y2_1920 = self.transform_point_640_to_1920(x2, y2)
        
        # 确保边界框的正确性
        x1_final = int(min(x1_1920, x2_1920))
        y1_final = int(min(y1_1920, y2_1920))
        x2_final = int(max(x1_1920, x2_1920))
        y2_final = int(max(y1_1920, y2_1920))
        
        # 确保坐标在1920×1080范围内
        x1_final = max(0, min(x1_final, 1920))
        y1_final = max(0, min(y1_final, 1080))
        x2_final = max(0, min(x2_final, 1920))
        y2_final = max(0, min(y2_final, 1080))
        
        return (x1_final, y1_final, x2_final, y2_final)
    
    def transform_mask_640_to_1920(self, mask_640: np.ndarray, is_probability_mask: bool = False) -> np.ndarray:
        """
        将640×480分辨率下的掩码转换为1920×1080分辨率

        Args:
            mask_640: 640×480分辨率下的掩码
            is_probability_mask: 是否为概率掩码（0-1之间的值）

        Returns:
            1920×1080分辨率下的掩码
        """
        if self.affine_matrix_640_to_1920 is None:
            raise ValueError("未加载标定结果")

        # 确保掩码是2D的
        if len(mask_640.shape) > 2:
            mask_640 = mask_640.squeeze()

        # 根据掩码类型选择插值方式
        if is_probability_mask:
            # 概率掩码使用线性插值
            interpolation = cv2.INTER_LINEAR
            mask_input = mask_640.astype(np.float32)
        else:
            # 二值掩码使用最近邻插值保持二值性
            interpolation = cv2.INTER_NEAREST
            mask_input = mask_640.astype(np.uint8)

        # 使用仿射变换将掩码映射到高分辨率
        mask_1920 = cv2.warpAffine(
            mask_input,
            self.affine_matrix_640_to_1920,
            (1920, 1080),
            flags=interpolation
        )

        # 根据输入类型返回相应格式
        if is_probability_mask:
            return mask_1920.astype(np.float32)
        else:
            return mask_1920.astype(bool)
    
    def map_detection_to_high_res(self, detection, image_shape_640: Tuple[int, int]) -> MappedRegion:
        """
        将单个检测结果映射到高分辨率
        
        Args:
            detection: YOLO检测结果对象
            image_shape_640: 640×480图像的形状 (height, width)
            
        Returns:
            映射后的区域信息
        """
        try:
            # 获取原始边界框
            if hasattr(detection, 'boxes') and detection.boxes is not None:
                # YOLOv8格式
                box = detection.boxes.xyxy[0].cpu().numpy()
                x1, y1, x2, y2 = map(int, box)
                confidence = float(detection.boxes.conf[0])
                class_id = int(detection.boxes.cls[0])
                class_name = detection.names[class_id] if hasattr(detection, 'names') else f"class_{class_id}"
            else:
                # 其他格式，尝试直接获取属性
                x1, y1, x2, y2 = detection.bbox if hasattr(detection, 'bbox') else (0, 0, 100, 100)
                confidence = detection.confidence if hasattr(detection, 'confidence') else 0.0
                class_name = detection.class_name if hasattr(detection, 'class_name') else "unknown"
            
            original_bbox = (x1, y1, x2, y2)
            
            # 转换边界框到高分辨率
            mapped_bbox = self.transform_bbox_640_to_1920(original_bbox)
            
            # 处理掩码（如果存在）
            mapped_mask = None
            if hasattr(detection, 'masks') and detection.masks is not None:
                try:
                    # YOLOv8分割格式
                    mask_640 = detection.masks.data[0].cpu().numpy()

                    # 确保掩码是2D的
                    if len(mask_640.shape) > 2:
                        mask_640 = mask_640.squeeze()

                    # 检查掩码尺寸是否匹配目标尺寸
                    target_height, target_width = image_shape_640
                    if mask_640.shape != (target_height, target_width):
                        # 调整掩码尺寸到640×480
                        mask_640 = cv2.resize(
                            mask_640.astype(np.uint8),
                            (target_width, target_height),
                            interpolation=cv2.INTER_NEAREST
                        )

                    # 判断是否为概率掩码
                    is_probability = mask_640.dtype == np.float32 and mask_640.max() <= 1.0
                    mapped_mask = self.transform_mask_640_to_1920(mask_640, is_probability)

                except Exception as e:
                    print(f"⚠️ 掩码转换失败: {e}")
            
            return MappedRegion(
                bbox=mapped_bbox,
                mask=mapped_mask,
                confidence=confidence,
                class_name=class_name,
                original_bbox=original_bbox
            )
            
        except Exception as e:
            print(f"❌ 检测结果映射失败: {e}")
            # 返回默认映射区域
            return MappedRegion(
                bbox=(0, 0, 100, 100),
                confidence=0.0,
                class_name="error"
            )
    
    def map_detections_batch(self, detections: List, image_shape_640: Tuple[int, int]) -> List[MappedRegion]:
        """
        批量映射检测结果到高分辨率
        
        Args:
            detections: YOLO检测结果列表
            image_shape_640: 640×480图像的形状 (height, width)
            
        Returns:
            映射后的区域信息列表
        """
        mapped_regions = []
        
        print(f"🔄 开始批量映射 {len(detections)} 个检测结果...")
        
        for i, detection in enumerate(detections):
            try:
                mapped_region = self.map_detection_to_high_res(detection, image_shape_640)
                mapped_regions.append(mapped_region)
                
                print(f"   {i+1}. {mapped_region.class_name}: "
                      f"{mapped_region.original_bbox} → {mapped_region.bbox} "
                      f"(置信度: {mapped_region.confidence:.3f})")
                
            except Exception as e:
                print(f"❌ 第 {i+1} 个检测结果映射失败: {e}")
                continue
        
        print(f"✅ 成功映射 {len(mapped_regions)} 个检测结果")
        return mapped_regions
    
    def create_roi_from_mapped_region(self, high_res_image: np.ndarray, mapped_region: MappedRegion,
                                     padding: int = 10) -> Tuple[Optional[np.ndarray], Optional[Tuple[int, int, int, int]]]:
        """
        从高分辨率图像中提取映射区域的ROI

        Args:
            high_res_image: 1920×1080高分辨率图像
            mapped_region: 映射后的区域信息
            padding: 边界扩展像素数

        Returns:
            (ROI图像, 调整后的边界框) 或 (None, None) 如果失败
        """
        try:
            # 输入验证
            if high_res_image is None or high_res_image.size == 0:
                print("❌ 输入图像无效")
                return None, None

            if mapped_region is None or mapped_region.bbox is None:
                print("❌ 映射区域无效")
                return None, None

            h, w = high_res_image.shape[:2]
            x1, y1, x2, y2 = mapped_region.bbox

            # 验证边界框的有效性
            if x1 >= x2 or y1 >= y2:
                print(f"❌ 无效的边界框: {mapped_region.bbox}")
                return None, None

            # 添加padding并确保在图像范围内
            x1_pad = max(0, x1 - padding)
            y1_pad = max(0, y1 - padding)
            x2_pad = min(w, x2 + padding)
            y2_pad = min(h, y2 + padding)

            # 再次验证调整后的边界框
            if x1_pad >= x2_pad or y1_pad >= y2_pad:
                print(f"⚠️ 调整后的ROI区域无效: {(x1_pad, y1_pad, x2_pad, y2_pad)}")
                return None, None

            # 提取ROI
            roi = high_res_image[y1_pad:y2_pad, x1_pad:x2_pad]

            # 检查ROI是否有效
            if roi.size == 0:
                print(f"⚠️ ROI区域为空: {(x1_pad, y1_pad, x2_pad, y2_pad)}")
                return None, None

            adjusted_bbox = (x1_pad, y1_pad, x2_pad, y2_pad)

            return roi, adjusted_bbox

        except Exception as e:
            print(f"❌ ROI提取失败: {e}")
            import traceback
            traceback.print_exc()
            return None, None
    

    def is_calibration_loaded(self) -> bool:
        """检查标定结果是否已加载"""
        return self.affine_matrix_640_to_1920 is not None

    def validate_bbox(self, bbox: Tuple[int, int, int, int], max_width: int = 1920, max_height: int = 1080) -> bool:
        """
        验证边界框的有效性

        Args:
            bbox: 边界框 (x1, y1, x2, y2)
            max_width: 最大宽度
            max_height: 最大高度

        Returns:
            是否有效
        """
        try:
            x1, y1, x2, y2 = bbox

            # 检查坐标顺序
            if x1 >= x2 or y1 >= y2:
                return False

            # 检查坐标范围
            if x1 < 0 or y1 < 0 or x2 > max_width or y2 > max_height:
                return False

            # 检查最小尺寸
            if (x2 - x1) < 1 or (y2 - y1) < 1:
                return False

            return True

        except (ValueError, TypeError):
            return False

    def get_transformation_info(self) -> dict:
        """
        获取变换信息

        Returns:
            变换信息字典
        """
        if not self.is_calibration_loaded():
            return {"status": "not_loaded"}

        return {
            "status": "loaded",
            "calibration_file": self.calibration_file,
            "calibration_info": self.calibration_info,
            "matrix_640_to_1920": self.affine_matrix_640_to_1920.tolist() if self.affine_matrix_640_to_1920 is not None else None,
            "matrix_1920_to_640": self.affine_matrix_1920_to_640.tolist() if self.affine_matrix_1920_to_640 is not None else None
        }

    def map_point_1920_to_640(self, point_1920: Tuple[float, float]) -> Tuple[float, float]:
        """
        将1920×1080坐标映射到640×480坐标

        Args:
            point_1920: 1920×1080坐标 (x, y)

        Returns:
            640×480坐标 (x, y)
        """
        if not self.is_calibration_loaded():
            # 如果没有仿射变换矩阵，使用简单缩放
            x_1920, y_1920 = point_1920
            x_640 = x_1920 * (640 / 1920)
            y_640 = y_1920 * (480 / 1080)
            return (x_640, y_640)

        try:
            # 使用仿射变换的逆矩阵
            point_homogeneous = np.array([point_1920[0], point_1920[1], 1.0])
            transformed_point = self.affine_matrix_1920_to_640 @ point_homogeneous
            return (transformed_point[0], transformed_point[1])
        except Exception as e:
            print(f"⚠️ 仿射逆变换失败，使用简单缩放: {e}")
            x_1920, y_1920 = point_1920
            x_640 = x_1920 * (640 / 1920)
            y_640 = y_1920 * (480 / 1080)
            return (x_640, y_640)

    def map_bbox_1920_to_640(self, bbox_1920: Tuple[int, int, int, int]) -> Tuple[int, int, int, int]:
        """
        将1920×1080边界框映射到640×480边界框

        Args:
            bbox_1920: 1920×1080边界框 (x1, y1, x2, y2)

        Returns:
            640×480边界框 (x1, y1, x2, y2)
        """
        x1_1920, y1_1920, x2_1920, y2_1920 = bbox_1920

        # 映射两个角点
        x1_640, y1_640 = self.map_point_1920_to_640((x1_1920, y1_1920))
        x2_640, y2_640 = self.map_point_1920_to_640((x2_1920, y2_1920))

        # 确保坐标在有效范围内
        x1_640 = max(0, min(int(x1_640), 639))
        y1_640 = max(0, min(int(y1_640), 479))
        x2_640 = max(0, min(int(x2_640), 639))
        y2_640 = max(0, min(int(y2_640), 479))

        return (x1_640, y1_640, x2_640, y2_640)

    def map_mask_1920_to_640(self, mask_1920: np.ndarray) -> np.ndarray:
        """
        将1920×1080掩膜映射到640×480掩膜

        Args:
            mask_1920: 1920×1080掩膜

        Returns:
            640×480掩膜
        """
        if not self.is_calibration_loaded():
            # 如果没有仿射变换矩阵，使用简单缩放
            return cv2.resize(mask_1920.astype(np.uint8), (640, 480),
                            interpolation=cv2.INTER_NEAREST).astype(bool)

        try:
            # 使用仿射变换的逆矩阵
            mask_640 = cv2.warpAffine(
                mask_1920.astype(np.uint8),
                self.affine_matrix_1920_to_640,
                (640, 480),
                flags=cv2.INTER_NEAREST
            )
            return mask_640.astype(bool)
        except Exception as e:
            print(f"⚠️ 仿射逆变换掩膜失败，使用简单缩放: {e}")
            return cv2.resize(mask_1920.astype(np.uint8), (640, 480),
                            interpolation=cv2.INTER_NEAREST).astype(bool)