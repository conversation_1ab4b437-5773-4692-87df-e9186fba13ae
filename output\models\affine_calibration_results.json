{"calibration_info": {"board_size": [8, 5], "square_size": 15.0, "valid_image_pairs": 14, "total_corner_points": 560, "image_pairs_used": [1, 2, 5, 7, 8, 11, 12, 14, 15, 16, 17, 18, 19, 20]}, "affine_transform": {"matrix_1920_to_640": [[0.3740106763340178, -4.1497114760588727e-05, -39.82452033965999], [4.1497114760588727e-05, 0.3740106763340178, 37.291183069724454]], "matrix_640_to_1920": [[2.673720433575681, 0.0002966537874195159, 106.46857121880424], [-0.0002966537874195159, 2.673720433575681, -99.71801226052469]], "description": "Transform from 1920x1080 to 640x480 coordinates"}, "accuracy_evaluation": {"mean_error": 4.118085813783153, "std_error": 3.6000118848859564, "max_error": 12.549776737750365, "min_error": 0.005546672111974251, "rmse": 5.469800393158984, "num_points": 560}, "usage_example": {"python_code": ["# 将1920×1080坐标转换为640×480坐标", "import numpy as np", "point_1920 = np.array([x, y, 1])  # 齐次坐标", "affine_matrix = np.array(matrix_1920_to_640)", "point_640 = affine_matrix @ point_1920", "# point_640 现在包含640×480分辨率下的坐标"]}}