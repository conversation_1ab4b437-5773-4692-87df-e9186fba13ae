#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像坐标系与点云坐标系转换工具
基于pyorbbecsdk实现深度像素到3D点云坐标的转换
"""

import numpy as np
from pyorbbecsdk import *
import cv2
from typing import Tuple, Optional, List, Union, Dict
import math
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from sklearn.linear_model import RANSACRegressor
from sklearn.preprocessing import StandardScaler
import os
import json
from datetime import datetime
import open3d as o3d

# 导入新的相机控制器
from camera_controller import CameraController

# 导入pyorbbecsdk类型
try:
    from pyorbbecsdk import FrameSet, OBFormat, AlignFilter, OBStreamType
except ImportError:
    # 如果导入失败，定义为Any类型以避免linter错误
    from typing import Any
    FrameSet = Any
    OBFormat = Any
    AlignFilter = Any
    OBStreamType = Any


class PathConfig:
    """路径配置类"""
    # 获取当前文件所在目录的绝对路径
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
    DATA_DIR = os.path.join(BASE_DIR, "数据")  # 数据文件夹
    
    @classmethod
    def setup_directories(cls):
        """创建必要的目录"""
        os.makedirs(cls.DATA_DIR, exist_ok=True)
        print(f"📁 数据保存目录: {cls.DATA_DIR}")
        return cls.DATA_DIR


class CoordinateTransformer:
    """
    图像坐标系与点云坐标系转换器
    支持：
    1. 深度像素坐标 -> 3D点云坐标
    2. 3D点云坐标 -> 图像像素坐标
    3. 批量坐标转换
    4. 基于平面检测的自动坐标系转换
    5. 使用pyorbbecsdk内置函数和手动实现
    """
    
    def __init__(self, config=None, pipeline=None, camera_param=None):
        """
        初始化坐标转换器
        
        Args:
            config: 系统配置对象
            pipeline: pyorbbecsdk Pipeline对象
            camera_param: 相机参数，如果为None则从pipeline获取
        """
        # 保存配置
        if config is not None:
            self.config = config
        else:
            self.config = None
        
        # 使用新的相机控制器
        self.camera_controller = None
        if config and hasattr(config, 'camera'):
            self.camera_controller = CameraController(config.camera)
        
        self.pipeline = pipeline
        self.camera_param = camera_param
        
        # 平面检测相关参数
        self.detected_plane_normal = None
        self.detected_plane_center = None
        self.plane_transform_matrix = None
        self.plane_detection_confidence = 0.0
        
        # 双重平面检测相关参数
        self.dual_plane_result = None
        
        # 内参矩阵和畸变系数
        self.depth_intrinsic = None
        self.color_intrinsic = None
        self.depth_to_color_extrinsic = None
        
        # 对齐过滤器（提前创建）
        self.align_filter = None
        
        self._sync_camera_parameters()
    
    def _sync_camera_parameters(self):
        """同步相机参数（通过camera_controller）"""
        # 在正常的项目流程中，camera_controller总是存在的
        if hasattr(self, 'camera_controller') and self.camera_controller:
            # 确保camera_controller有camera_param
            if self.camera_param is not None and self.camera_controller.camera_param is None:
                self.camera_controller.camera_param = self.camera_param

            # 如果camera_controller还没有提取参数，让它提取
            if (self.camera_controller.camera_param is not None and
                self.camera_controller.depth_intrinsic is None):
                self.camera_controller._extract_camera_parameters()

            # 同步camera_controller的参数到coordinate_transformer
            self.depth_intrinsic = self.camera_controller.depth_intrinsic
            self.color_intrinsic = self.camera_controller.color_intrinsic
            self.depth_to_color_extrinsic = self.camera_controller.depth_to_color_extrinsic

            if self.depth_intrinsic is not None:
                print("✅ 通过camera_controller同步相机参数")
            else:
                print("⚠️ camera_controller中无可用参数")
            return

        # 在正常的项目流程中，这种情况不应该发生
        print("❌ 缺少camera_controller，这在正常项目流程中不应该发生")
        if self.camera_param is not None:
            print("⚠️ 尝试基本参数提取作为应急措施")
            try:
                self.depth_intrinsic = getattr(self.camera_param, 'depth_intrinsic', None)
                self.color_intrinsic = getattr(self.camera_param, 'color_intrinsic', None)
                self.depth_to_color_extrinsic = getattr(self.camera_param, 'transform', None)
                print("✅ 基本参数提取完成")
            except Exception as e:
                print(f"❌ 基本参数提取失败: {e}")
        else:
            print("⚠️ 无相机参数可用")
    
    def pixel_to_point_builtin(self, frames: FrameSet, u: int, v: int) -> Optional[Tuple[float, float, float]]:
        """
        使用pyorbbecsdk内置函数将像素坐标转换为3D点坐标
        
        Args:
            frames: 帧集合
            u: 像素x坐标
            v: 像素y坐标
            
        Returns:
            (x, y, z) 3D坐标，单位：毫米，如果转换失败返回None
        """
        if frames is None or self.camera_param is None:
            return None
            
        try:
            # 获取深度帧
            depth_frame = frames.get_depth_frame()
            if depth_frame is None:
                return None
            
            # 检查坐标是否在有效范围内
            width = depth_frame.get_width()
            height = depth_frame.get_height()
            
            if u < 0 or u >= width or v < 0 or v >= height:
                return None
            
            # 获取深度值
            depth_data = np.frombuffer(depth_frame.get_data(), dtype=np.uint16)
            depth_data = depth_data.reshape((height, width))
            depth_value = depth_data[v, u]
            
            if depth_value == 0:
                return None
                
            # 深度值单位转换修复（与batch_transform_pixels_to_3d保持一致）
            scale = depth_frame.get_depth_scale()
            if abs(scale - 1.0) < 1e-6:  # scale ≈ 1.0
                # 当depth_scale=1.0时，原始深度值通常已经是毫米单位
                depth_mm = float(depth_value)
            else:
                # 当depth_scale≠1.0时，按标准方式转换：原始值*比例=米，再*1000=毫米
                depth_mm = depth_value * scale * 1000
            
            # 使用内参进行反投影
            fx = self.depth_intrinsic.fx
            fy = self.depth_intrinsic.fy
            cx = self.depth_intrinsic.cx
            cy = self.depth_intrinsic.cy
            
            # 计算3D坐标（毫米单位）
            x = (u - cx) * depth_mm / fx
            y = (v - cy) * depth_mm / fy
            z = depth_mm
            
            return (x, y, z)
            
        except Exception as e:
            print(f"❌ 像素到点坐标转换失败: {e}")
            return None
    
    def point_to_pixel_builtin(self, x: float, y: float, z: float, 
                              target_intrinsic=None) -> Optional[Tuple[int, int]]:
        """
        使用内参将3D点坐标转换为像素坐标
        
        Args:
            x, y, z: 3D坐标，单位：毫米
            target_intrinsic: 目标相机内参，默认使用深度相机内参
            
        Returns:
            (u, v) 像素坐标，如果转换失败返回None
        """
        if target_intrinsic is None:
            target_intrinsic = self.depth_intrinsic
            
        if target_intrinsic is None:
            return None
            
        try:
            if z <= 0:
                return None
                
            # 投影到像素坐标
            u = int(x * target_intrinsic.fx / z + target_intrinsic.cx)
            v = int(y * target_intrinsic.fy / z + target_intrinsic.cy)
            
            # 检查是否在图像范围内
            if 0 <= u < target_intrinsic.width and 0 <= v < target_intrinsic.height:
                return (u, v)
            else:
                return None
                
        except Exception as e:
            print(f"❌ 点到像素坐标转换失败: {e}")
            return None
    
    def get_pointcloud_from_frames(self, frames: FrameSet) -> Optional[np.ndarray]:
        """
        从帧集合中获取点云数据
        
        Args:
            frames: 帧集合
            
        Returns:
            点云数据，形状为(N, 3)，单位为毫米
        """
        if frames is None:
            print("❌ 输入帧集合为空")
            return None
        
        try:
            # 首先检查是否有深度帧
            depth_frame = frames.get_depth_frame()
            if depth_frame is None:
                print("❌ 无法获取深度帧")
                return None
            
            width = depth_frame.get_width()
            height = depth_frame.get_height()
            print(f"📏 深度帧尺寸: {width}x{height}")
            
            # 如果相机参数无效，尝试重新同步
            if self.camera_param is None:
                print("⚠️ 相机参数为空，尝试重新同步...")
                self._sync_camera_parameters()
                if self.camera_param is None and self.depth_intrinsic is None:
                    print("❌ 无法获取相机参数，无法创建点云")
                    return None

            # 方法1: 尝试获取彩色点云 (如果SDK支持)
            try:
                color_frame = frames.get_color_frame()
                # 检查函数是否存在
                if color_frame is not None and hasattr(frames, 'get_color_point_cloud'):
                    points = frames.get_color_point_cloud(self.camera_param)
                    if points is not None and len(points) > 0:
                        pointcloud = np.array(points)
                        print(f"✅ 获取彩色点云成功，点数: {len(pointcloud):,}")
                        # 返回前需要处理点云
                        return self._process_pointcloud(pointcloud)
            except Exception as e:
                print(f"⚠️ 获取彩色点云失败: {e}")

            # 方法2: 尝试获取普通点云
            try:
                if hasattr(frames, 'get_point_cloud'):
                    points = frames.get_point_cloud(self.camera_param)
                    if points is not None and len(points) > 0:
                        pointcloud = np.array(points)
                        print(f"✅ 获取普通点云成功，点数: {len(pointcloud):,}")
                        return self._process_pointcloud(pointcloud)
            except Exception as e:
                print(f"⚠️ 获取普通点云失败: {e}")

            # 所有方法都失败
            print("❌ 所有点云获取方法都失败")
            return None
                
        except Exception as e:
            print(f"❌ 获取点云的根过程失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _process_pointcloud(self, pointcloud: np.ndarray) -> Optional[np.ndarray]:
        """处理点云数据，包括形状检查和单位转换"""
        try:
            # 确保点云数据是正确的形状
            if len(pointcloud.shape) == 2 and pointcloud.shape[1] >= 3:
                # 只取XYZ坐标
                xyz_points = pointcloud[:, :3].astype(np.float32)
                
                # 过滤无效点
                valid_mask = np.all(np.isfinite(xyz_points), axis=1) & np.any(xyz_points != 0, axis=1)
                xyz_points = xyz_points[valid_mask]
                
                if len(xyz_points) == 0:
                    print("❌ 所有点云数据都无效")
                    return None
                        
                # 保持毫米单位
                non_zero_mask = np.any(xyz_points != 0, axis=1)
                if np.sum(non_zero_mask) > 0:
                    non_zero_points = xyz_points[non_zero_mask]
                    # 计算平均距离
                    avg_distance = np.sqrt(np.sum(non_zero_points**2, axis=1)).mean()
                    print(f"✅ 点云单位: 毫米 (平均距离: {avg_distance:.1f}mm)")
                
                print(f"✅ 处理后点云: {len(xyz_points)} 个有效点")
                return xyz_points
            else:
                print(f"❌ 点云数据形状错误: {pointcloud.shape}")
                return None
        except Exception as e:
            print(f"❌ 点云处理失败: {e}")
            return None
            

    
    def batch_pixel_to_point(self, frames: FrameSet, 
                           pixel_coords: List[Tuple[int, int]]) -> List[Optional[Tuple[float, float, float]]]:
        """
        批量将像素坐标转换为3D点坐标
        
        Args:
            frames: 帧集合
            pixel_coords: 像素坐标列表 [(u1, v1), (u2, v2), ...]
            
        Returns:
            3D坐标列表 [(x1, y1, z1), (x2, y2, z2), ...]
        """
        results = []
        for u, v in pixel_coords:
            point = self.pixel_to_point_builtin(frames, u, v)
            results.append(point)
        return results
    
    def get_depth_at_pixel(self, frames: FrameSet, u: int, v: int) -> Optional[float]:
        """
        获取指定像素位置的深度值
        
        Args:
            frames: 帧集合
            u: 像素x坐标
            v: 像素y坐标
            
        Returns:
            深度值，单位：毫米
        """
        try:
            depth_frame = frames.get_depth_frame()
            if depth_frame is None:
                return None
                
            width = depth_frame.get_width()
            height = depth_frame.get_height()
            
            if u < 0 or u >= width or v < 0 or v >= height:
                return None
            
            depth_data = np.frombuffer(depth_frame.get_data(), dtype=np.uint16)
            depth_data = depth_data.reshape((height, width))
            depth_value = depth_data[v, u]
            
            if depth_value == 0:
                return None
                
            scale = depth_frame.get_depth_scale()
            # 深度值单位转换修复（与其他函数保持一致）
            if abs(scale - 1.0) < 1e-6:  # scale ≈ 1.0
                # 当depth_scale=1.0时，原始深度值通常已经是毫米单位
                return float(depth_value)
            else:
                # 当depth_scale≠1.0时，按标准方式转换：原始值*比例=米，再*1000=毫米
                return depth_value * scale * 1000
            
        except Exception as e:
            print(f"❌ 获取深度值失败: {e}")
            return None
    
    def create_depth_colormap(self, depth_frame) -> Optional[np.ndarray]:
        """
        创建深度图的彩色映射
        
        Args:
            depth_frame: 深度帧
            
        Returns:
            彩色深度图
        """
        try:
            width = depth_frame.get_width()
            height = depth_frame.get_height()
            scale = depth_frame.get_depth_scale()
            
            depth_data = np.frombuffer(depth_frame.get_data(), dtype=np.uint16)
            depth_data = depth_data.reshape((height, width))

            # 深度值单位转换修复（用于可视化，转换为米）
            if abs(scale - 1.0) < 1e-6:  # scale ≈ 1.0
                # 当depth_scale=1.0时，原始深度值通常已经是毫米单位，转换为米
                depth_data_meters = depth_data.astype(np.float32) / 1000.0
            else:
                # 当depth_scale≠1.0时，按标准方式转换：原始值*比例=米
                depth_data_meters = depth_data.astype(np.float32) * scale
            
            # 归一化到0-255范围
            depth_normalized = cv2.normalize(depth_data_meters, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
            
            # 应用彩色映射
            depth_colormap = cv2.applyColorMap(depth_normalized, cv2.COLORMAP_JET)
            
            return depth_colormap
            
        except Exception as e:
            print(f"❌ 创建深度彩色映射失败: {e}")
            return None
    
    def visualize_pixel_to_point_mapping(self, frames: FrameSet, 
                                       pixel_coords: List[Tuple[int, int]],
                                       save_path: str = None) -> Optional[np.ndarray]:
        """
        可视化像素到点的映射关系
        
        Args:
            frames: 帧集合
            pixel_coords: 要可视化的像素坐标列表
            save_path: 保存路径，如果为None则自动生成保存到数据文件夹
            
        Returns:
            可视化图像
        """
        try:
            depth_frame = frames.get_depth_frame()
            if depth_frame is None:
                return None
                
            # 创建深度彩色映射
            depth_colormap = self.create_depth_colormap(depth_frame)
            if depth_colormap is None:
                return None
            
            # 在图像上标记像素点和对应的3D坐标
            for i, (u, v) in enumerate(pixel_coords):
                point_3d = self.pixel_to_point_builtin(frames, u, v)
                
                if point_3d is not None:
                    x, y, z = point_3d
                    # 绘制像素点
                    cv2.circle(depth_colormap, (u, v), 5, (0, 255, 0), -1)
                    
                    # 添加3D坐标文本
                    text = f"({x:.3f}, {y:.3f}, {z:.3f})"
                    cv2.putText(depth_colormap, text, (u + 10, v - 10), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                else:
                    # 无效点用红色标记
                    cv2.circle(depth_colormap, (u, v), 5, (0, 0, 255), -1)
            
            # 保存图像
            if save_path is None:
                # 自动生成保存路径到数据文件夹
                data_dir = PathConfig.setup_directories()
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                save_path = os.path.join(data_dir, f"pixel_to_point_mapping_{timestamp}.png")
            
                cv2.imwrite(save_path, depth_colormap)
                print(f"✅ 可视化结果已保存到: {save_path}")
            
            return depth_colormap
            
        except Exception as e:
            print(f"❌ 可视化失败: {e}")
            return None
    
    def get_camera_info(self) -> dict:
        """
        获取相机信息（通过camera_controller）
        
        Returns:
            相机信息字典
        """
        if hasattr(self, 'camera_controller') and self.camera_controller:
            # 使用camera_controller的方法获取完整信息
            return self.camera_controller.get_camera_info()
        else:
            # 如果没有camera_controller，返回基本的内参信息
            info = {
                "depth_intrinsic": None,
                "color_intrinsic": None,
                "has_extrinsic": False
            }
            
            if self.depth_intrinsic:
                info["depth_intrinsic"] = {
                    "fx": self.depth_intrinsic.fx,
                    "fy": self.depth_intrinsic.fy,
                    "cx": self.depth_intrinsic.cx,
                    "cy": self.depth_intrinsic.cy,
                    "width": self.depth_intrinsic.width,
                    "height": self.depth_intrinsic.height
                }
            
            if self.color_intrinsic:
                info["color_intrinsic"] = {
                    "fx": self.color_intrinsic.fx,
                    "fy": self.color_intrinsic.fy,
                    "cx": self.color_intrinsic.cx,
                    "cy": self.color_intrinsic.cy,
                    "width": self.color_intrinsic.width,
                    "height": self.color_intrinsic.height
                }
            
            if self.depth_to_color_extrinsic:
                info["has_extrinsic"] = True
            
            return info
    
    def compute_plane_transform_matrix(self, plane_normal: np.ndarray, plane_center: np.ndarray) -> np.ndarray:
        """
        计算从相机坐标系到平面坐标系的变换矩阵。
        确保整个桌面平面在新坐标系中Z≈0。
        
        平面坐标系定义：
        - Z轴 (Up): 垂直于平面的法向量，代表高度。
        - XY平面: 与物理平面平行。
        - X轴 (Right): 大致沿着相机的水平方向。
        - Y轴 (Forward): 根据右手坐标系确定。
        
        Args:
            plane_normal: 平面法向量，形状为(3,)，无单位（归一化向量）
            plane_center: 平面中心点，形状为(3,)，单位：毫米
                              
        Returns:
            4x4变换矩阵，用于将毫米单位的点从相机坐标系转换到平面坐标系。
        """
        try:
            # 确保输入类型正确
            plane_normal = np.array(plane_normal, dtype=np.float32)
            plane_center = np.array(plane_center, dtype=np.float32)
            
            print(f"🔧 构建完整桌面坐标系变换矩阵...")
            print(f"   平面法向量: ({plane_normal[0]:.3f}, {plane_normal[1]:.3f}, {plane_normal[2]:.3f})")
            print(f"   平面中心点: ({plane_center[0]:.3f}, {plane_center[1]:.3f}, {plane_center[2]:.3f})")

            # 1. 统一法向量方向：确保法向量指向相机方向（即"向上"）
            # 计算相机原点(0,0,0)到plane_center的向量
            to_camera = -plane_center  # 从平面中心指向相机原点
            
            # 如果dot(plane_normal, to_camera) < 0，则翻转法向量
            if np.dot(plane_normal, to_camera) < 0:
                z_new = -plane_normal  # 翻转法向量使其指向相机方向
                print(f"   翻转法向量使其指向相机方向: ({(-plane_normal)[0]:.3f}, {(-plane_normal)[1]:.3f}, {(-plane_normal)[2]:.3f})")
            else:
                z_new = plane_normal   # 法向量已经指向相机方向，保持不变
                print(f"   法向量已指向相机方向，保持不变: ({plane_normal[0]:.3f}, {plane_normal[1]:.3f}, {plane_normal[2]:.3f})")
                
            # 确保归一化
            z_new = z_new / np.linalg.norm(z_new)
            
            # 2. 构建新坐标系基向量
            # 选择参考向量（避免与z_new平行）
            if abs(np.dot(z_new, np.array([1.0, 0.0, 0.0]))) > 0.95:
                ref_vec = np.array([0.0, 1.0, 0.0])  # 使用Y轴
                print(f"   z_new与X轴近似平行，使用Y轴作为参考向量")
            else:
                ref_vec = np.array([1.0, 0.0, 0.0])  # 使用X轴
                print(f"   使用X轴作为参考向量")
            
            # 构建y_new = normalize(cross(z_new, ref_vec))
            y_new = np.cross(z_new, ref_vec)
            y_new = y_new / np.linalg.norm(y_new)
            
            # 构建x_new = cross(y_new, z_new)
            x_new = np.cross(y_new, z_new)
            x_new = x_new / np.linalg.norm(x_new)  # 确保归一化
            
            print(f"   新坐标系基向量:")
            print(f"     x_new: ({x_new[0]:.3f}, {x_new[1]:.3f}, {x_new[2]:.3f})")
            print(f"     y_new: ({y_new[0]:.3f}, {y_new[1]:.3f}, {y_new[2]:.3f})")
            print(f"     z_new: ({z_new[0]:.3f}, {z_new[1]:.3f}, {z_new[2]:.3f})")

            # 3. 构造旋转矩阵R（3x3），列为[x_new, y_new, z_new]
            R = np.column_stack([x_new, y_new, z_new])
            
            # 4. 计算平移向量，关键：让plane_center变换后Z=0
            # 计算t_z = -(R^T @ plane_center)[2]
            transformed_center = R.T @ plane_center
            t_z = -transformed_center[2]
            
            print(f"   平面中心点经旋转后的坐标: ({transformed_center[0]:.3f}, {transformed_center[1]:.3f}, {transformed_center[2]:.3f})")
            print(f"   需要的Z方向平移量: {t_z:.3f}mm")
            
            # 平移向量为(0, 0, t_z)
            translation = np.array([0.0, 0.0, t_z], dtype=np.float32)
            
            # 5. 构造最终变换矩阵
            transform_matrix = np.eye(4, dtype=np.float32)
            transform_matrix[:3, :3] = R.T  # 旋转矩阵的转置（逆矩阵）
            transform_matrix[:3, 3] = translation
            
            # 6. 验证变换是否正确：桌面中心点在新坐标系中应该有Z≈0
            test_center_homogeneous = np.append(plane_center, 1)
            transformed_center_final = transform_matrix @ test_center_homogeneous
            print(f"   验证：桌面中心点在桌面坐标系中的坐标: ({transformed_center_final[0]:.3f}, {transformed_center_final[1]:.3f}, {transformed_center_final[2]:.3f})")
            print(f"   验证：桌面中心点Z坐标 = {transformed_center_final[2]:.3f}mm (应该接近0)")
            
            if abs(transformed_center_final[2]) > 5.0:  # 如果Z坐标不接近0，说明有问题
                print(f"⚠️ 警告：桌面中心点Z坐标偏差较大，可能存在计算错误")
            
            # 7. 添加调试输出：验证整个桌面平面的Z值
            if hasattr(self, 'dual_plane_result') and self.dual_plane_result is not None:
                if 'table_inliers_idx' in self.dual_plane_result:
                    # 获取桌面内点
                    table_inliers_idx = self.dual_plane_result['table_inliers_idx']
                    if 'all_points' in self.dual_plane_result and table_inliers_idx is not None:
                        all_points = self.dual_plane_result['all_points']
                        table_points = all_points[table_inliers_idx]
                        
                        # 将桌面点变换到新坐标系
                        table_points_homogeneous = np.column_stack([table_points, np.ones(len(table_points))])
                        transformed_table_points = (transform_matrix @ table_points_homogeneous.T).T
                        
                        # 计算平均Z值
                        avg_z = np.mean(transformed_table_points[:, 2])
                        z_std = np.std(transformed_table_points[:, 2])
                        
                        print(f"🔍 桌面点验证结果:")
                        print(f"   桌面点数量: {len(table_points)}")
                        print(f"   桌面点平均Z值: {avg_z:.3f}mm (期望为0)")
                        print(f"   桌面点Z值标准差: {z_std:.3f}mm")
                        
                        if abs(avg_z) > 5.0:
                            print(f"⚠️ 警告：桌面点平均Z值偏差较大({avg_z:.3f}mm)，变换矩阵可能有问题")
                        else:
                            print(f"✅ 桌面点Z值验证通过，平均偏差仅{avg_z:.3f}mm")
            
            print(f"✅ 完整桌面坐标系变换矩阵构建完成")
            return transform_matrix
            
        except Exception as e:
            print(f"❌ 变换矩阵计算失败: {e}")
            import traceback
            traceback.print_exc()
            return np.eye(4, dtype=np.float32)
    
    def auto_detect_table_plane(self, frames: FrameSet, 
                              detection_params: dict = None) -> bool:
        """
        自动检测桌面/地面平面（使用双重RANSAC算法）
        
        Args:
            frames: 帧集合
            detection_params: 检测参数
            
        Returns:
            是否检测成功
        """
        try:
            # 自动检测桌面平面函数现在将使用双重RANSAC的独立参数配置
            # 这里不再需要合并参数，因为双重RANSAC内部有独立的参数管理
            if detection_params:
                print(f"⚠️ 注意: detection_params在双重RANSAC模式下被忽略，使用配置文件中的独立参数")
            
            print(f"🔧 开始双重RANSAC桌面检测...")
            
            # 获取点云数据（已经处理单位转换）
            pointcloud = self.get_pointcloud_from_frames(frames)
            if pointcloud is None:
                print(f"❌ 无法获取点云数据")
                return False
            
            # 使用新的双重平面检测算法（参数在函数内部管理）
            dual_plane_result = self.detect_dual_planes_with_table_boundary(pointcloud)
            
            if dual_plane_result is None:
                print(f"❌ 双重平面检测失败")
                return False
            
            # 提取桌面信息（使用最终选择的桌面参数）
            table_info = dual_plane_result['table']
            table_normal = table_info['normal']
            table_center = table_info['center']
            table_confidence = table_info['confidence']
            
            # 检查是否有预计算的变换矩阵
            if 'table_transform_matrix' in dual_plane_result:
                transform_matrix = dual_plane_result['table_transform_matrix']
                print(f"✅ 使用预计算的桌面变换矩阵")
            else:
                # 计算变换矩阵
                transform_matrix = self.compute_plane_transform_matrix(table_normal, table_center)
                if transform_matrix is None:
                    print(f"❌ 无法计算变换矩阵")
                    return False
            
            # 保存检测结果（确保使用最终选择的桌面参数）
            self.detected_plane_normal = table_normal
            self.detected_plane_center = table_center
            self.plane_transform_matrix = transform_matrix
            self.plane_detection_confidence = table_confidence

            print(f"✅ 保存最终桌面参数:")
            print(f"   最终桌面法向量: ({table_normal[0]:.3f}, {table_normal[1]:.3f}, {table_normal[2]:.3f})")
            print(f"   最终桌面中心: ({table_center[0]:.3f}, {table_center[1]:.3f}, {table_center[2]:.3f})")
            print(f"   最终桌面置信度: {table_confidence:.1%}")
            
            # 保存双重平面检测结果
            self.dual_plane_result = dual_plane_result
            
            print(f"✅ 双重平面检测成功!")
            print(f"   地面检测置信度: {dual_plane_result['ground']['confidence']:.1%}")
            print(f"   桌面检测置信度: {table_confidence:.1%}")
            print(f"   高度差: {dual_plane_result['height_difference']:.3f}mm")
            print(f"✅ 桌面坐标系建立完成!")
            
            return True
            
        except Exception as e:
            print(f"❌ 双重平面检测失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def transform_camera_to_plane_coordinate(self, camera_point: Union[np.ndarray, Tuple[float, float, float]]) -> Optional[np.ndarray]:
        """
        将相机坐标系的点转换到检测到的平面坐标系
        
        Args:
            camera_point: 相机坐标系中的点
            
        Returns:
            平面坐标系中的点 (x, y, z)
        """
        if self.plane_transform_matrix is None:
            print(f"❌ 未检测到平面，请先调用 auto_detect_table_plane()")
            return None
        
        try:
            # 转换为numpy数组
            if isinstance(camera_point, tuple) or isinstance(camera_point, list):
                camera_point = np.array(camera_point)
            elif not isinstance(camera_point, np.ndarray):
                print(f"❌ 不支持的输入类型: {type(camera_point)}")
                return None
            
            # 确保是一维数组并只取前3个坐标
            if camera_point.ndim > 1:
                camera_point = camera_point.flatten()
            camera_point = camera_point[:3]
            
            # 验证坐标有效性
            if len(camera_point) < 3:
                print(f"❌ 坐标点维度不足: {len(camera_point)}")
                return None
            
            if not np.all(np.isfinite(camera_point)):
                print(f"❌ 坐标点包含无效值: {camera_point}")
                return None
            
            # 转换为齐次坐标
            homogeneous_point = np.append(camera_point, 1)
            
            # 检查变换矩阵和齐次坐标的维度
            if self.plane_transform_matrix.shape[1] != len(homogeneous_point):
                print(f"❌ 变换矩阵维度不匹配: {self.plane_transform_matrix.shape} vs {homogeneous_point.shape}")
                return None
            
            # 应用变换矩阵
            plane_point = self.plane_transform_matrix @ homogeneous_point
            
            # 验证输出有效性
            result = plane_point[:3]
            if not np.all(np.isfinite(result)):
                print(f"❌ 转换结果包含无效值: {result}")
                return None
            
            # DEBUG: 强制 Z=0 以验证是否是平面拟合问题
            # 如果启用后香蕉 Z 值变为 0，则说明问题出在平面拟合或变换矩阵，而非后续逻辑
            # 可以通过设置 self._debug_force_z_zero = True 来启用此调试模式
            if hasattr(self, '_debug_force_z_zero') and self._debug_force_z_zero:
                print(f"🔧 调试模式：强制将Z坐标从 {result[2]:.3f}mm 设为 0.0mm")
                result = result.copy()  # 避免修改原始数组
                result[2] = 0.0
            
            return result
            
        except Exception as e:
            print(f"❌ 坐标转换失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def transform_plane_to_camera_coordinate(self, plane_point: Union[np.ndarray, Tuple[float, float, float]]) -> Optional[np.ndarray]:
        """
        将平面坐标系的点转换到相机坐标系（逆变换）
        
        Args:
            plane_point: 平面坐标系中的点
            
        Returns:
            相机坐标系中的点 (x, y, z)
        """
        if self.plane_transform_matrix is None:
            print(f"❌ 未检测到平面，请先调用 auto_detect_table_plane()")
            return None
        
        try:
            # 计算逆变换矩阵
            inverse_transform = np.linalg.inv(self.plane_transform_matrix)
            
            # 转换为齐次坐标
            if isinstance(plane_point, tuple):
                plane_point = np.array(plane_point)
            
            # 确保是一维数组并只取前3个坐标
            if plane_point.ndim > 1:
                plane_point = plane_point.flatten()
            plane_point = plane_point[:3]
            
            homogeneous_point = np.append(plane_point, 1)
            
            # 检查逆变换矩阵和齐次坐标的维度
            if inverse_transform.shape[1] != len(homogeneous_point):
                print(f"❌ 逆变换矩阵维度不匹配: {inverse_transform.shape} vs {homogeneous_point.shape}")
                return None
            
            # 应用逆变换矩阵
            camera_point = inverse_transform @ homogeneous_point
            
            return camera_point[:3]
            
        except Exception as e:
            print(f"❌ 逆坐标转换失败: {e}")
            return None
    

    
    def batch_transform_pixels_to_3d(self, pixel_coords: List[Tuple[int, int]], 
                                  frames: FrameSet) -> Tuple[np.ndarray, np.ndarray]:
        """
        批量将像素坐标转换为相机坐标系和桌面坐标系的3D点
        
        Args:
            pixel_coords: 像素坐标列表 [(u1, v1), (u2, v2), ...]
            frames: 帧集合
            
        Returns:
            (camera_points, desktop_points): 相机坐标系和桌面坐标系的点云数组
        """
        camera_points = []
        desktop_points = []
        
        # 添加调试信息
        print(f"🔄 批量转换 {len(pixel_coords)} 个像素坐标到3D点...")
        
        # 检查输入参数
        if frames is None:
            print("❌ 帧数据为空，无法进行坐标转换")
            # 返回空数组而不是None，以保持函数接口一致性
            return np.array([]), np.array([])
        
        # 检查深度帧
        depth_frame = frames.get_depth_frame()
        if depth_frame is None:
            print("❌ 深度帧为空，无法进行坐标转换")
            return np.array([]), np.array([])
        
        # 获取深度帧尺寸和比例
        depth_width = depth_frame.get_width()
        depth_height = depth_frame.get_height()
        depth_scale = depth_frame.get_depth_scale()

        # 检查内参是否有效
        if self.depth_intrinsic is None:
            print("❌ 深度相机内参为空，无法进行坐标转换")
            return np.array([]), np.array([])
        
        # 获取内参参数
        fx = self.depth_intrinsic.fx
        fy = self.depth_intrinsic.fy
        cx = self.depth_intrinsic.cx
        cy = self.depth_intrinsic.cy
        
        # 统计成功转换的点数
        success_count = 0
        
        # 获取深度数据
        try:
            depth_data = np.frombuffer(depth_frame.get_data(), dtype=np.uint16)
            depth_image = depth_data.reshape((depth_height, depth_width))
        except Exception as e:
            print(f"❌ 无法获取深度数据: {e}")
            return np.array([]), np.array([])
        
        # 检查像素坐标是否需要映射（从高分辨率到低分辨率）
        # 假设YOLO检测使用的是1920x1080，深度图像是640x480
        need_coordinate_mapping = False
        if len(pixel_coords) > 0:
            first_u, first_v = pixel_coords[0]
            if first_u > depth_width or first_v > depth_height:
                need_coordinate_mapping = True
                print(f"🔄 检测到高分辨率坐标，进行坐标映射: {first_u}x{first_v} -> {depth_width}x{depth_height}")
        
        for u, v in pixel_coords:
            # 如果需要坐标映射，将高分辨率坐标映射到低分辨率
            if need_coordinate_mapping:
                # 计算缩放比例
                scale_x = depth_width / 1920.0  # 假设YOLO检测使用1920x1080
                scale_y = depth_height / 1080.0
                
                # 映射坐标
                u_mapped = int(u * scale_x)
                v_mapped = int(v * scale_y)
                
                # 确保映射后的坐标在有效范围内
                u_mapped = max(0, min(u_mapped, depth_width - 1))
                v_mapped = max(0, min(v_mapped, depth_height - 1))
            else:
                u_mapped, v_mapped = u, v
            
            # 检查像素坐标是否在有效范围内
            if u_mapped < 0 or u_mapped >= depth_width or v_mapped < 0 or v_mapped >= depth_height:
                camera_points.append(np.array([np.nan, np.nan, np.nan]))
                desktop_points.append(np.array([np.nan, np.nan, np.nan]))
                continue
            
            # 获取深度值
            depth_value = depth_image[v_mapped, u_mapped]
            
            # 检查深度值是否有效
            if depth_value == 0:
                camera_points.append(np.array([np.nan, np.nan, np.nan]))
                desktop_points.append(np.array([np.nan, np.nan, np.nan]))
                continue
            
            # 深度值单位转换修复
            # depth_value: 原始深度值（SDK中通常是无单位整数）
            # depth_scale: 深度比例因子（SDK用于转换为米的系数）
            #
            # 关键修复：根据depth_scale的值判断正确的转换方式
            if abs(depth_scale - 1.0) < 1e-6:  # depth_scale ≈ 1.0
                # 当depth_scale=1.0时，原始深度值通常已经是毫米单位
                depth_mm = float(depth_value)
            else:
                # 当depth_scale≠1.0时，按标准方式转换：原始值*比例=米，再*1000=毫米
                depth_mm = depth_value * depth_scale * 1000

            # 计算相机坐标系中的3D点（毫米单位）
            # 注意：这里使用映射后的坐标(u_mapped, v_mapped)进行3D计算
            x = (u_mapped - cx) * depth_mm / fx
            y = (v_mapped - cy) * depth_mm / fy
            z = depth_mm
            
            camera_point = np.array([x, y, z])
            
            # 检查相机坐标是否有效
            if not np.all(np.isfinite(camera_point)):
                camera_points.append(np.array([np.nan, np.nan, np.nan]))
                desktop_points.append(np.array([np.nan, np.nan, np.nan]))
                continue
            
            # 添加相机坐标点
            camera_points.append(camera_point)
            
            # 转换到桌面坐标系
            try:
                if self.plane_transform_matrix is not None:
                    # 转换为齐次坐标（输入：毫米单位的相机坐标）
                    homogeneous_point = np.append(camera_point, 1)
                    
                    # 应用变换矩阵（矩阵设计用于毫米单位的坐标）
                    desktop_point = self.plane_transform_matrix @ homogeneous_point
                    desktop_point = desktop_point[:3]  # 输出：毫米单位的桌面坐标
                    
                    # 检查桌面坐标是否有效
                    if np.all(np.isfinite(desktop_point)):
                        desktop_points.append(desktop_point)
                        success_count += 1
                    else:
                        desktop_points.append(np.array([np.nan, np.nan, np.nan]))
                else:
                    # 如果没有平面变换矩阵，使用相机坐标
                    desktop_points.append(camera_point)
                    success_count += 1
            except Exception as e:
                print(f"⚠️ 坐标转换异常: {e}")
                desktop_points.append(np.array([np.nan, np.nan, np.nan]))
        
        # 转换为numpy数组
        camera_points_array = np.array(camera_points)
        desktop_points_array = np.array(desktop_points)
        
        # 添加转换结果统计和调试信息
        print(f"✅ 坐标转换完成: 总共 {len(pixel_coords)} 个点, 成功转换 {success_count} 个点")

        # 如果转换成功率很低，添加调试信息
        if len(pixel_coords) > 0 and success_count / len(pixel_coords) < 0.1:
            print(f"⚠️ 转换成功率很低 ({success_count}/{len(pixel_coords)} = {success_count/len(pixel_coords)*100:.1f}%)")
            print(f"   深度图像尺寸: {depth_width}x{depth_height}")
            print(f"   深度比例因子: {depth_scale}")
            print(f"   坐标映射: {'启用' if need_coordinate_mapping else '未启用'}")

            # 检查前几个像素的深度值
            sample_size = min(5, len(pixel_coords))
            print(f"   前{sample_size}个像素的深度值:")
            for i in range(sample_size):
                u, v = pixel_coords[i]
                if need_coordinate_mapping:
                    scale_x = depth_width / 1920.0
                    scale_y = depth_height / 1080.0
                    u_mapped = int(u * scale_x)
                    v_mapped = int(v * scale_y)
                    u_mapped = max(0, min(u_mapped, depth_width - 1))
                    v_mapped = max(0, min(v_mapped, depth_height - 1))
                else:
                    u_mapped, v_mapped = u, v
                
                if 0 <= u_mapped < depth_width and 0 <= v_mapped < depth_height:
                    depth_val = depth_image[v_mapped, u_mapped]
                    if depth_val == 0:
                        print(f"     像素({u},{v}) -> ({u_mapped},{v_mapped}): 无效深度值")
                    else:
                        print(f"     像素({u},{v}) -> ({u_mapped},{v_mapped}): 深度值={depth_val}")
                else:
                    print(f"     像素({u},{v}) -> ({u_mapped},{v_mapped}): 超出范围")

            # 检查深度图像的整体统计
            valid_depths = depth_image[depth_image > 0]
            if len(valid_depths) > 0:
                print(f"   深度图像统计: 有效像素{len(valid_depths)}/{depth_image.size}, "
                      f"深度范围{np.min(valid_depths)}~{np.max(valid_depths)}")
            else:
                print(f"   ❌ 深度图像中没有有效深度值！")
        
        return camera_points_array, desktop_points_array
    
    def get_plane_detection_info(self) -> dict:
        """
        获取平面检测信息
        
        Returns:
            包含平面检测信息的字典
        """
        info = {
            'plane_detected': self.plane_transform_matrix is not None,
            'plane_normal': self.detected_plane_normal.tolist() if self.detected_plane_normal is not None else None,
            'plane_center': self.detected_plane_center.tolist() if self.detected_plane_center is not None else None,
            'transform_matrix': self.plane_transform_matrix.tolist() if self.plane_transform_matrix is not None else None,
            'confidence': self.plane_detection_confidence
        }
        
        # 添加双重平面检测信息
        if self.dual_plane_result is not None:
            info['dual_plane_detection'] = {
                'ground_confidence': self.dual_plane_result['ground']['confidence'],
                'table_confidence': self.dual_plane_result['table']['confidence'],
                'height_difference': self.dual_plane_result['height_difference'],
                'is_single_plane': self.dual_plane_result['is_single_plane']
            }
        
        return info
    
    def get_table_height(self) -> Optional[float]:
        """
        获取检测到的桌面高度（在相机坐标系中的Y坐标）
        
        Returns:
            桌面高度，单位：毫米。如果未检测到桌面则返回None
            在相机坐标系中，Y轴向下，所以桌面的Y坐标表示桌面距离相机的垂直距离
        """
        if self.detected_plane_center is None:
            return None

        # 返回桌面中心点的Y坐标（相机坐标系中的高度）
        # 注意：这里返回的是相机坐标系中的Y坐标，单位是毫米
        return float(self.detected_plane_center[1])



    def align_depth_to_color(self, frames: FrameSet, target_resolution: str = "640x480") -> Optional[Tuple[np.ndarray, np.ndarray]]:
        """
        使用pyorbbecsdk的AlignFilter将深度图像对齐到彩色图像

        Args:
            frames: 帧集合
            target_resolution: 目标分辨率 ("640x480" 或 "1920x1080")

        Returns:
            (aligned_depth_image, color_image) 如果成功，否则返回None
        """
        try:
            if not self.camera_param:
                print("❌ 缺少相机参数，无法执行对齐")
                return None

            color_frame = frames.get_color_frame()
            depth_frame = frames.get_depth_frame()

            if color_frame is None or depth_frame is None:
                print("❌ 缺少彩色帧或深度帧")
                return None

            print(f"🔍 对齐目标分辨率: {target_resolution}")
            print(f"   原始彩色帧尺寸: {color_frame.get_width()}x{color_frame.get_height()}")
            print(f"   原始深度帧尺寸: {depth_frame.get_width()}x{depth_frame.get_height()}")

            # 检查分辨率兼容性，如果不是640×480则直接使用备选方案
            color_width = color_frame.get_width()
            color_height = color_frame.get_height()
            depth_width = depth_frame.get_width()
            depth_height = depth_frame.get_height()

            print(f"   实际帧尺寸: 彩色{color_width}×{color_height}, 深度{depth_width}×{depth_height}")

            # 如果彩色帧不是640×480，或者目标分辨率不是640×480，使用备选方案
            if (color_width != 640 or color_height != 480) or target_resolution != "640x480":
                print(f"⚠️ 分辨率不匹配或非标准分辨率，使用备选对齐方案")
                return self._fallback_align_depth_to_color(color_frame, depth_frame, target_resolution)

            # 使用预创建的AlignFilter进行对齐（仅限640×480）
            try:
                if self.align_filter is None:
                    print("⚠️ AlignFilter未创建，尝试创建...")
                    self._create_align_filter()

                if self.align_filter is None:
                    print("❌ AlignFilter创建失败，使用备选对齐方案")
                    return self._fallback_align_depth_to_color(color_frame, depth_frame, target_resolution)

                # 处理帧集合，获取对齐后的帧
                aligned_frames = self.align_filter.process(frames)

                if aligned_frames is None:
                    print("❌ AlignFilter处理失败")
                    return self._fallback_align_depth_to_color(color_frame, depth_frame, target_resolution)

                # 从对齐后的帧集合中获取深度帧和彩色帧
                aligned_depth_frame = aligned_frames.get_depth_frame()
                aligned_color_frame = aligned_frames.get_color_frame()

                if aligned_depth_frame is None or aligned_color_frame is None:
                    print("❌ 无法从对齐帧集合中获取深度帧或彩色帧")
                    return self._fallback_align_depth_to_color(color_frame, depth_frame, target_resolution)

                # 转换为numpy数组
                color_image = self._convert_frame_to_image(aligned_color_frame)
                aligned_depth_image = self._convert_depth_frame_to_array(aligned_depth_frame)

                if color_image is not None and aligned_depth_image is not None:
                    print("✅ 使用AlignFilter对齐成功")
                    print(f"   对齐后彩色图像尺寸: {color_image.shape}")
                    print(f"   对齐后深度图像尺寸: {aligned_depth_image.shape}")

                    # 如果需要高分辨率，将深度图像上采样到1920×1080
                    if target_resolution == "1920x1080":
                        print("🔄 将深度图像上采样到1920×1080...")
                        upsampled_depth = cv2.resize(aligned_depth_image, (1920, 1080),
                                                    interpolation=cv2.INTER_NEAREST)
                        upsampled_color = cv2.resize(color_image, (1920, 1080),
                                                   interpolation=cv2.INTER_LINEAR)
                        print(f"✅ 上采样完成: 深度{upsampled_depth.shape} 彩色{upsampled_color.shape}")
                        return upsampled_depth, upsampled_color
                    else:
                        # 验证对齐后的图像尺寸
                        if color_image.shape[:2] == aligned_depth_image.shape[:2]:
                            print(f"✅ 对齐后图像尺寸匹配: {color_image.shape[:2]}")
                        else:
                            print(f"⚠️ 对齐后图像尺寸不匹配: 彩色{color_image.shape[:2]} vs 深度{aligned_depth_image.shape[:2]}")

                        return aligned_depth_image, color_image
                else:
                    print("❌ 图像转换失败")
                    return self._fallback_align_depth_to_color(color_frame, depth_frame, target_resolution)

            except Exception as e:
                print(f"⚠️ AlignFilter对齐失败，使用备选方案: {e}")
                return self._fallback_align_depth_to_color(color_frame, depth_frame, target_resolution)

        except Exception as e:
            print(f"❌ 深度图像对齐失败: {e}")
            return None

    def _convert_frame_to_image(self, frame) -> Optional[np.ndarray]:
        """将帧转换为图像数组"""
        try:
            from pyorbbecsdk import OBFormat
            
            width = frame.get_width()
            height = frame.get_height()
            color_format = frame.get_format()
            data = np.asanyarray(frame.get_data())
            
            if color_format == OBFormat.RGB:
                image = np.resize(data, (height, width, 3))
                image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
            elif color_format == OBFormat.BGR:
                image = np.resize(data, (height, width, 3))
            elif color_format == OBFormat.MJPG:
                image = cv2.imdecode(data, cv2.IMREAD_COLOR)
            else:
                print(f"⚠️ 不支持的彩色格式: {color_format}")
                return None
            
            return image
            
        except Exception as e:
            print(f"❌ 帧转换失败: {e}")
            return None

    def _convert_depth_frame_to_array(self, depth_frame) -> Optional[np.ndarray]:
        """将深度帧转换为数组"""
        try:
            width = depth_frame.get_width()
            height = depth_frame.get_height()
            depth_data = np.frombuffer(depth_frame.get_data(), dtype=np.uint16)
            depth_image = depth_data.reshape((height, width))
            return depth_image
        except Exception as e:
            print(f"❌ 深度帧转换失败: {e}")
            return None

    def _fallback_align_depth_to_color(self, color_frame, depth_frame, target_resolution: str = "640x480") -> Optional[Tuple[np.ndarray, np.ndarray]]:
        """
        备选对齐方案：当AlignFilter失败时使用

        Args:
            color_frame: 彩色帧
            depth_frame: 深度帧
            target_resolution: 目标分辨率
        """
        try:
            # 获取图像数据
            color_image = self._convert_frame_to_image(color_frame)
            depth_image = self._convert_depth_frame_to_array(depth_frame)

            if color_image is None or depth_image is None:
                print("❌ 图像转换失败")
                return None

            print(f"🔄 备选对齐方案，目标分辨率: {target_resolution}")
            print(f"   原始彩色图像: {color_image.shape}")
            print(f"   原始深度图像: {depth_image.shape}")

            if target_resolution == "1920x1080":
                # 将深度图像上采样到1920×1080
                upsampled_depth = cv2.resize(depth_image, (1920, 1080),
                                           interpolation=cv2.INTER_NEAREST)
                upsampled_color = cv2.resize(color_image, (1920, 1080),
                                           interpolation=cv2.INTER_LINEAR)
                print(f"✅ 上采样到1920×1080: 深度{upsampled_depth.shape} 彩色{upsampled_color.shape}")
                return upsampled_depth, upsampled_color
            else:
                # 检查图像尺寸是否匹配
                if color_image.shape[:2] == depth_image.shape[:2]:
                    print("✅ 图像尺寸已匹配，使用原始数据")
                    return depth_image, color_image
                else:
                    print(f"⚠️ 图像尺寸不匹配: 彩色{color_image.shape[:2]} vs 深度{depth_image.shape[:2]}")

                    # 尝试将深度图像缩放到彩色图像尺寸
                    target_height, target_width = color_image.shape[:2]
                    aligned_depth_image = cv2.resize(depth_image, (target_width, target_height),
                                                   interpolation=cv2.INTER_NEAREST)

                    print(f"✅ 深度图像已缩放到彩色图像尺寸: {aligned_depth_image.shape[:2]}")
                    return aligned_depth_image, color_image

        except Exception as e:
            print(f"❌ 备选对齐失败: {e}")
            return None

    def _manual_align_depth_to_color(self, color_frame, depth_frame) -> Optional[Tuple[np.ndarray, np.ndarray]]:
        """手动对齐深度图像到彩色图像（保留兼容性）"""
        print("⚠️ _manual_align_depth_to_color已弃用，使用_fallback_align_depth_to_color")
        return self._fallback_align_depth_to_color(color_frame, depth_frame, "640x480")

    def connect_camera(self) -> bool:
        """
        连接相机（通过camera_controller）
            
        Returns:
            连接成功返回True，失败返回False
        """
        # 在正常的项目流程中，camera_controller总是存在的
        if not self.camera_controller:
            print("❌ 缺少camera_controller，无法连接相机")
            return False
            
        # 使用camera_controller连接相机
        if self.camera_controller.connect():
            # 同步camera_controller的参数到coordinate_transformer
            self.pipeline = self.camera_controller.pipeline
            self.camera_param = self.camera_controller.camera_param
            self.depth_intrinsic = self.camera_controller.depth_intrinsic
            self.color_intrinsic = self.camera_controller.color_intrinsic
            self.depth_to_color_extrinsic = self.camera_controller.depth_to_color_extrinsic
            
            # 创建AlignFilter（在连接成功后）
            self._create_align_filter()
            
            print("✅ 通过camera_controller连接相机成功")
            return True
        else:
            print("❌ camera_controller连接相机失败")
            return False

    def _create_align_filter(self):
        """创建对齐过滤器"""
        try:
            if self.align_filter is None:
                # 创建对齐过滤器，对齐到彩色流
                self.align_filter = AlignFilter(align_to_stream=OBStreamType.COLOR_STREAM)
                print("✅ AlignFilter创建成功")
            else:
                print("⚠️ AlignFilter已存在，跳过创建")
        except Exception as e:
            print(f"⚠️ AlignFilter创建失败: {e}")
            self.align_filter = None
    


    def cleanup(self):
        """清理资源"""
        if not hasattr(self, '_cleanup_done'):
            self._cleanup_done = False
        
        if self._cleanup_done:
            return
            
        try:
            print("🧹 清理坐标转换器资源...")
            
            # 清理对齐过滤器
            if self.align_filter:
                self.align_filter = None
                print("✅ AlignFilter已清理")
            
            # 清理相机控制器
            if self.camera_controller:
                self.camera_controller.cleanup()
                self.camera_controller = None
            
            # 清理pipeline
            if self.pipeline:
                try:
                    self.pipeline.stop()
                    print("✅ Pipeline已停止")
                except Exception as e:
                    print(f"⚠️ Pipeline停止失败: {e}")
                finally:
                    self.pipeline = None
            
            self._cleanup_done = True
            print("✅ 坐标转换器资源清理完成")
            
        except Exception as e:
            print(f"❌ 坐标转换器资源清理失败: {e}")
            self._cleanup_done = True
    
    def detect_dual_planes_with_table_boundary(self, pointcloud: np.ndarray, 
                                             min_samples: int = 300,
                                             residual_threshold: float = 10.0,  # 10mm距离阈值，毫米单位
                                             max_trials: int = 3000,
                                             min_inliers_ratio: float = 0.01) -> Optional[Dict]:
        """
        使用双重RANSAC检测地面和桌面平面
        
        Args:
            pointcloud: 点云数据，单位：毫米
            min_samples: RANSAC最小样本数
            residual_threshold: 距离阈值，单位：毫米
            max_trials: 最大迭代次数
            min_inliers_ratio: 最小内点比例
            
        Returns:
            双重平面检测结果字典
        """
        try:
            if pointcloud is None or len(pointcloud) < min_samples * 10:
                print(f"❌ 点云数据不足，无法进行双重平面检测")
                return None
            
            # 获取两次RANSAC的不同参数
            if self.config and hasattr(self.config, 'plane_detection'):
                # 第一次RANSAC检测地面的参数
                first_params = {
                    'min_samples': self.config.plane_detection.first_ransac_min_samples,
                    'residual_threshold': self.config.plane_detection.first_ransac_residual_threshold,
                    'max_trials': self.config.plane_detection.first_ransac_max_trials,
                    'min_inliers_ratio': self.config.plane_detection.first_ransac_min_inliers_ratio
                }
                # 第二次RANSAC检测桌面的参数
                second_params = {
                    'min_samples': self.config.plane_detection.second_ransac_min_samples,
                    'residual_threshold': self.config.plane_detection.second_ransac_residual_threshold,
                    'max_trials': self.config.plane_detection.second_ransac_max_trials,
                    'min_inliers_ratio': self.config.plane_detection.second_ransac_min_inliers_ratio
                }
                print(f"✅ 使用配置文件中的双重RANSAC参数")
            else:
                # 备用硬编码参数
                first_params = {
                    'min_samples': 3,
                    'residual_threshold': 20.0,
                    'max_trials': 3000,
                    'min_inliers_ratio': 0.3
                }
                second_params = {
                    'min_samples': 3, 
                    'residual_threshold': 8.0,
                    'max_trials': 3000,
                    'min_inliers_ratio': 0.1
                }
                print(f"⚠️ 使用硬编码双重RANSAC参数")
            
            print(f"✈️  正在使用双重RANSAC检测地面和桌面...")
            print(f"📊 第一次RANSAC检测地面参数:")
            print(f"   最小样本数: {first_params['min_samples']}")
            print(f"   距离阈值: {first_params['residual_threshold']:.1f}mm")
            print(f"   最大迭代: {first_params['max_trials']}")
            print(f"   最小内点比例: {first_params['min_inliers_ratio']:.3f}")
            
            print(f"📊 第二次RANSAC检测桌面参数:")
            print(f"   最小样本数: {second_params['min_samples']}")
            print(f"   距离阈值: {second_params['residual_threshold']:.1f}mm")
            print(f"   最大迭代: {second_params['max_trials']}")
            print(f"   最小内点比例: {second_params['min_inliers_ratio']:.3f}")
            
            # 第一次RANSAC：检测地面
            ground_result = self._detect_single_plane(pointcloud, 
                                                    first_params['residual_threshold'], 
                                                    first_params['max_trials'], 
                                                    first_params['min_inliers_ratio'])
            
            if ground_result is None:
                print("❌ 第一次平面检测失败")
                return None
            
            ground_normal, ground_center, ground_inliers, ground_confidence = ground_result
            print(f"✅ 地面检测成功:")
            print(f"   法向量: ({ground_normal[0]:.3f}, {ground_normal[1]:.3f}, {ground_normal[2]:.3f})")
            print(f"   中心点: ({ground_center[0]:.3f}, {ground_center[1]:.3f}, {ground_center[2]:.3f})mm")
            print(f"   内点数量: {len(ground_inliers):,} ({ground_confidence:.1%})")
            
            # 移除地面点，准备第二次检测
            remaining_points = np.delete(pointcloud, ground_inliers, axis=0)
            print(f"   移除地面后剩余点数: {len(remaining_points):,}")
            
            if len(remaining_points) < min_samples * 10:  # 确保有足够的点进行第二次检测
                print(f"⚠️ 移除地面后剩余点数过少: {len(remaining_points)}")
                print(f"⚠️ 无法进行第二次RANSAC桌面检测，将使用第一次RANSAC结果作为备选桌面")
                # 如果剩余点太少，使用地面作为备选桌面（说明可能整个场景就是地面）
                ground_transform_matrix = self.compute_plane_transform_matrix(ground_normal, ground_center)
                return self._create_dual_plane_result(ground_normal, ground_center, ground_confidence, 
                                                    ground_normal, ground_center, ground_confidence, 
                                                    pointcloud, is_single_plane=True, 
                                                    table_transform_matrix=ground_transform_matrix,
                                                    height_difference=0.0, 
                                                    table_inliers_idx=ground_inliers)
            
            print(f"\n📊 第二次RANSAC：检测桌面...")
            print(f"   剩余点数: {len(remaining_points):,}")
            
            # 第二次RANSAC：检测桌面
            table_result = self._detect_single_plane(remaining_points, 
                                                    second_params['residual_threshold'], 
                                                    second_params['max_trials'], 
                                                    second_params['min_inliers_ratio'])
            
            if table_result is None:
                print("❌ 第二次RANSAC桌面检测失败！")
                print("⚠️ 作为备选方案，将使用第一次RANSAC检测的地面作为桌面坐标系")
                print("💡 建议：调整相机角度或检测参数以获得更好的桌面检测效果")
                ground_transform_matrix = self.compute_plane_transform_matrix(ground_normal, ground_center)
                return self._create_dual_plane_result(ground_normal, ground_center, ground_confidence, 
                                                    ground_normal, ground_center, ground_confidence, 
                                                    pointcloud, is_single_plane=True,
                                                    table_transform_matrix=ground_transform_matrix,
                                                    height_difference=0.0, 
                                                    table_inliers_idx=ground_inliers)
            
            table_normal, table_center, table_inliers, table_confidence = table_result
            print(f"✅ 桌面检测成功:")
            print(f"   法向量: ({table_normal[0]:.3f}, {table_normal[1]:.3f}, {table_normal[2]:.3f})")
            print(f"   中心点: ({table_center[0]:.3f}, {table_center[1]:.3f}, {table_center[2]:.3f})mm")
            print(f"   内点数量: {len(table_inliers):,} ({table_confidence:.1%})")
            
            # 计算距离差，确定哪个是桌面（距离相机更近的作为桌面）
            print(f"\n📏 分析两个平面距离相机的远近，确定哪个是桌面...")

            # 比较两个平面在相机坐标系中的Z坐标（相机坐标系中Z轴指向前方，值小的表示距离更近）
            ground_z = ground_center[2]  # 第一次检测平面Z坐标
            table_z = table_center[2]    # 第二次检测平面Z坐标

            print(f"   第一次检测平面（地面候选）Z坐标: {ground_z:.3f}mm")
            print(f"   第二次检测平面（桌面候选）Z坐标: {table_z:.3f}mm")

            # 修复桌面选择逻辑：
            # 1. 优先选择点数较多且置信度高的平面作为地面
            # 2. 选择距离相机更近（Z值更小）的平面作为桌面
            # 3. 如果两个平面都有合理的置信度，选择较近的作为桌面
            
            # 分析两个平面的特征
            ground_points_ratio = ground_confidence
            table_points_ratio = table_confidence
            distance_diff = abs(ground_z - table_z)
            
            print(f"   第一次检测平面置信度: {ground_points_ratio:.1%}")
            print(f"   第二次检测平面置信度: {table_points_ratio:.1%}")
            print(f"   两平面距离差: {distance_diff:.3f}mm")
            
            # 选择策略：如果距离差很大（>200mm），选择较近的作为桌面
            # 如果距离差较小，选择置信度更高的作为地面，另一个作为桌面
            if distance_diff > 200:
                # 距离差较大，基于距离选择
                if ground_z < table_z:
                    # 第一次检测的平面更近，应该是桌面
                    print(f"✅ 基于距离差({distance_diff:.1f}mm)：第一次检测的平面更靠近相机，将其作为桌面")
                    final_table_normal, final_table_center = ground_normal, ground_center
                    final_table_confidence = ground_confidence
                    final_ground_normal, final_ground_center = table_normal, table_center
                    final_ground_confidence = table_confidence
                else:
                    # 第二次检测的平面更近，应该是桌面
                    print(f"✅ 基于距离差({distance_diff:.1f}mm)：第二次检测的平面更靠近相机，将其作为桌面")
                    final_table_normal, final_table_center = table_normal, table_center
                    final_table_confidence = table_confidence
                    final_ground_normal, final_ground_center = ground_normal, ground_center
                    final_ground_confidence = ground_confidence
            else:
                # 距离差较小，基于置信度和常识选择
                # 通常第一次RANSAC能检测到更大的平面（地面），第二次检测到桌面
                print(f"✅ 基于常识：第一次检测为地面，第二次检测为桌面")
                final_table_normal, final_table_center = table_normal, table_center
                final_table_confidence = table_confidence
                final_ground_normal, final_ground_center = ground_normal, ground_center
                final_ground_confidence = ground_confidence

            # 计算最终桌面的变换矩阵
            final_table_transform_matrix = self.compute_plane_transform_matrix(final_table_normal, final_table_center)

            if final_table_transform_matrix is None:
                print("❌ 无法计算桌面变换矩阵")
                return None

            # 计算距离差（用于显示）
            distance_diff = abs(ground_z - table_z)

            print(f"\n📋 最终平面分配结果:")
            print(f"   桌面中心: ({final_table_center[0]:.3f}, {final_table_center[1]:.3f}, {final_table_center[2]:.3f})mm")
            print(f"   地面中心: ({final_ground_center[0]:.3f}, {final_ground_center[1]:.3f}, {final_ground_center[2]:.3f})mm")
            print(f"   距离差: {distance_diff:.3f}mm")
            print(f"✅ 基于距离分析确定桌面坐标系")
            
            # 确定最终的桌面内点索引
            # 需要根据最终选择的桌面是哪个来决定使用哪个内点索引
            final_table_inliers_idx = None
            if final_table_normal is table_normal and np.array_equal(final_table_center, table_center):
                # 最终桌面是第二次检测的结果，使用table_inliers
                # 但需要注意table_inliers是相对于remaining_points的索引，需要映射回原始点云
                if table_inliers is not None:
                    # 获取剩余点在原始点云中的索引
                    remaining_indices = np.setdiff1d(np.arange(len(pointcloud)), ground_inliers)
                    # 映射table_inliers到原始点云索引
                    final_table_inliers_idx = remaining_indices[table_inliers]
                    print(f"   使用第二次检测的桌面，内点数量: {len(final_table_inliers_idx)}")
            else:
                # 最终桌面是第一次检测的结果，使用ground_inliers
                final_table_inliers_idx = ground_inliers
                print(f"   使用第一次检测的桌面，内点数量: {len(final_table_inliers_idx)}")
            
            return self._create_dual_plane_result(final_ground_normal, final_ground_center, final_ground_confidence,
                                                final_table_normal, final_table_center, final_table_confidence,
                                                pointcloud, table_transform_matrix=final_table_transform_matrix,
                                                height_difference=distance_diff, 
                                                table_inliers_idx=final_table_inliers_idx)
            
        except Exception as e:
            print(f"❌ 双重平面检测失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _detect_single_plane(self, points: np.ndarray, residual_threshold: float, 
                            max_trials: int, min_inliers_ratio: float) -> Optional[Tuple]:
        """
        检测单个平面
        
        Args:
            points: 点云数据，单位：毫米
            residual_threshold: 距离阈值，单位：毫米
            max_trials: 最大迭代次数
            min_inliers_ratio: 最小内点比例
            
        Returns:
            (平面法向量, 平面中心点（毫米）, 内点索引, 置信度)
        """
        try:
            if len(points) < 10:
                return None
            
            # 转换为Open3D点云格式（需要转换为米单位）
            pcd = o3d.geometry.PointCloud()
            points_meters = points / 1000.0  # 转换为米
            pcd.points = o3d.utility.Vector3dVector(points_meters)
            
            # 使用RANSAC拟合平面（Open3D使用米单位）
            plane_model, inliers = pcd.segment_plane(
                distance_threshold=residual_threshold / 1000.0,  # 转换为米
                ransac_n=3,
                num_iterations=max_trials
            )
            
            # Open3D返回的plane_model是4维的 [a, b, c, d]，其中[a,b,c]是法向量
            # 我们需要提取前3个元素作为法向量
            plane_normal = plane_model[:3]  # 提取前3个元素作为法向量
            
            # 保持原始法向量，不在这里进行翻转
            # 翻转判定将在compute_plane_transform_matrix中统一处理
            
            # 计算内点比例
            inlier_ratio = len(inliers) / len(points)
            
            if inlier_ratio < min_inliers_ratio:
                print(f"⚠️ 内点比例过低: {inlier_ratio:.1%} < {min_inliers_ratio:.1%}")
                return None
            
            # 计算平面中心点（使用内点）
            # 注意：这里使用原始的毫米单位点云计算中心点，确保单位一致性
            inlier_points = points[inliers]  # points是毫米单位
            center = np.mean(inlier_points, axis=0)  # center也是毫米单位
            
            return plane_normal, center, inliers, inlier_ratio
            
        except Exception as e:
            print(f"❌ 平面检测失败: {e}")
            return None
    
    def _create_dual_plane_result(self, ground_normal, ground_center, ground_confidence,
                                 table_normal, table_center, table_confidence,
                                 all_points, is_single_plane=False, table_transform_matrix=None, 
                                 height_difference=0.0, table_inliers_idx=None):
        """
        创建双重平面检测结果
        
        Args:
            ground_normal: 地面法向量
            ground_center: 地面中心点
            ground_confidence: 地面检测置信度
            table_normal: 桌面法向量
            table_center: 桌面中心点
            table_confidence: 桌面检测置信度
            all_points: 所有点云数据
            is_single_plane: 是否为单平面模式
            table_transform_matrix: 桌面变换矩阵
            height_difference: 高度差
            table_inliers_idx: 桌面内点索引（用于后续验证）
            
        Returns:
            结果字典
        """
        result = {
            'ground': {
                'normal': ground_normal,
                'center': ground_center,
                'confidence': ground_confidence
            },
            'table': {
                'normal': table_normal,
                'center': table_center,
                'confidence': table_confidence
            },
            'is_single_plane': is_single_plane,
            'all_points': all_points,
            'height_difference': height_difference
        }
        
        # 如果有桌面变换矩阵，保存它
        if table_transform_matrix is not None:
            result['table_transform_matrix'] = table_transform_matrix
        
        # 保存桌面内点索引，用于后续验证
        if table_inliers_idx is not None:
            result['table_inliers_idx'] = table_inliers_idx
        
        return result
    
    def _get_mask_pixels(self, detection, use_erosion: bool = True) -> List[Tuple[int, int]]:
        """
        获取物体掩膜的像素坐标
        
        Args:
            detection: 检测结果对象
            use_erosion: 是否对掩膜进行腐蚀处理，缩小边界
            
        Returns:
            掩膜像素坐标列表
        """
        if detection.mask is None:
            # 如果没有掩膜，使用边界框的中心点
            center_x = (detection.bbox[0] + detection.bbox[2]) // 2
            center_y = (detection.bbox[1] + detection.bbox[3]) // 2
            return [(center_x, center_y)]
        
        # 获取掩膜中非零像素的坐标
        mask = detection.mask
        
        # 可选的掩膜腐蚀处理
        if use_erosion:
            mask = self._apply_mask_erosion(mask)
        
        y_coords, x_coords = np.where(mask > 0)
        
        # 限制采样数量，避免计算量过大
        max_samples = 100
        if len(x_coords) > max_samples:
            indices = np.random.choice(len(x_coords), max_samples, replace=False)
            x_coords = x_coords[indices]
            y_coords = y_coords[indices]
        
        return list(zip(x_coords, y_coords))



    def _apply_mask_erosion(self, mask: np.ndarray, erosion_size: int = 1, iterations: int = 1) -> np.ndarray:
        """
        对掩膜进行轻微腐蚀处理，统一的腐蚀操作方法

        Args:
            mask: 原始掩膜
            erosion_size: 腐蚀核大小，默认1x1（减少偏移）
            iterations: 腐蚀迭代次数，默认1次

        Returns:
            腐蚀后的掩膜
        """
        try:
            if mask is None or mask.size == 0:
                return mask

            # 确保掩膜是二进制的
            binary_mask = (mask > 0).astype(np.uint8)

            # 验证原始掩膜
            original_area = np.sum(binary_mask)
            if original_area == 0:
                print("⚠️ 原始掩膜为空")
                return binary_mask

            # 创建椭圆形腐蚀核（使用更小的核）
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (erosion_size, erosion_size))

            # 执行腐蚀操作
            processed_mask = cv2.erode(binary_mask, kernel, iterations=iterations)

            # 验证腐蚀效果
            eroded_area = np.sum(processed_mask)
            erosion_ratio = eroded_area / original_area if original_area > 0 else 0
            
            print(f"     掩膜腐蚀: 核大小={erosion_size}, 迭代={iterations}")
            print(f"     面积变化: {original_area} → {eroded_area} (比例: {erosion_ratio:.3f})")

            # 如果腐蚀过度，返回原掩膜
            if erosion_ratio < 0.7:
                print(f"     ⚠️ 腐蚀过度，返回原掩膜")
                return binary_mask

            # 确保腐蚀后仍有像素点
            if np.any(processed_mask > 0):
                return processed_mask
            else:
                # 如果腐蚀后没有像素点了，使用原始掩膜
                print(f"     ⚠️ 腐蚀后掩膜为空，返回原掩膜")
                return binary_mask
                    
        except Exception as e:
            print(f"⚠️ 掩膜腐蚀处理失败，使用原始掩膜: {e}")
            return (mask > 0).astype(np.uint8) if mask is not None else mask
