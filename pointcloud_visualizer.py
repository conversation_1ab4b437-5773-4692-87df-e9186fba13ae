#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
点云可视化模块
提供多种点云显示功能，包括彩色点云和交互式3D点云
"""

import numpy as np
import cv2
from typing import List, Optional, Tuple
import traceback

# 导入自定义模块
from object_analyzer import ObjectAnalysisResult

class PointCloudVisualizer:
    """点云可视化器"""
    
    def __init__(self, coordinate_transformer):
        """
        初始化点云可视化器
        
        Args:
            coordinate_transformer: 坐标转换器，用于获取相机参数
        """
        self.coordinate_transformer = coordinate_transformer
        
    def display_interactive_pointcloud_with_bboxes(self, analysis_results: List[ObjectAnalysisResult], 
                                                  frames=None, color_image=None):
        """
        显示可交互的3D点云窗口，包含检测框的3D位置（使用桌面坐标系）
        
        Args:
            analysis_results: 分析结果列表
            frames: 相机帧数据
            color_image: 彩色图像
        """
        try:
            import open3d as o3d
            
            print("🎨 创建交互式3D点云窗口（桌面坐标系）...")
            
            if frames is None:
                print("⚠️ 没有帧数据，无法显示点云")
                return
            
            # 获取深度帧和彩色帧
            depth_frame = frames.get_depth_frame()
            color_frame = frames.get_color_frame()
            
            if depth_frame is None:
                print("⚠️ 没有深度帧数据")
                return
            
            # 检查是否已检测到桌面平面
            if self.coordinate_transformer.plane_transform_matrix is None:
                print("⚠️ 未检测到桌面平面，请先调用自动平面检测")
                print("💡 尝试自动检测桌面平面...")
                if not self.coordinate_transformer.auto_detect_table_plane(frames):
                    print("❌ 自动平面检测失败，使用相机坐标系")
                    return
            
            # 创建几何体列表
            geometries = []
            
            # 1. 不再生成背景点云，只显示物体点云
            
            # 2. 为每个检测到的物体创建点云和边界框（桌面坐标系）
            print(f"📦 处理 {len(analysis_results)} 个检测物体...")
            object_colors = [
                [1.0, 0.0, 0.0],  # 红色
                [0.0, 1.0, 0.0],  # 绿色
                [0.0, 0.0, 1.0],  # 蓝色
                [1.0, 1.0, 0.0],  # 黄色
                [1.0, 0.0, 1.0],  # 品红
                [0.0, 1.0, 1.0],  # 青色
                [1.0, 0.5, 0.0],  # 橙色
                [0.5, 0.0, 1.0],  # 紫色
            ]
            
            # # 调试信息：打印每个分析结果的属性
            # for i, result in enumerate(analysis_results):
            #     print(f"   检查物体 {i+1} 的属性:")
            #     for attr in dir(result):
            #         if not attr.startswith('_') and not callable(getattr(result, attr)):
            #             print(f"     - {attr}: {type(getattr(result, attr))}")
                        
            #             # 如果是点云数据，打印其形状
            #             value = getattr(result, attr)
            #             if isinstance(value, np.ndarray) and value.size > 0:
            #                 print(f"       形状: {value.shape}")
            #             elif isinstance(value, list) and len(value) > 0:
            #                 print(f"       长度: {len(value)}")
            
            # 处理每个物体
            for i, result in enumerate(analysis_results):
                # 获取类名
                class_name = result.class_name if hasattr(result, 'class_name') else f"物体{i+1}"
                print(f"   处理第 {i+1} 个物体: {class_name}")
                
                # 检查是否有点云数据
                has_points = False
                desktop_points = []
                
                # 尝试获取点云数据 - 首先检查camera_points属性
                if hasattr(result, 'camera_points') and result.camera_points is not None and len(result.camera_points) > 0:
                    print(f"     使用camera_points属性: {len(result.camera_points)} 个点")
                    camera_points = result.camera_points
                    has_points = True
                    
                    # 将相机坐标系的点转换为桌面坐标系
                    for point in camera_points:
                        if np.all(np.isfinite(point)):  # 确保点坐标有效
                            try:
                                # 使用齐次坐标直接应用变换矩阵
                                homogeneous_point = np.append(point, 1)
                                desktop_point = self.coordinate_transformer.plane_transform_matrix @ homogeneous_point
                                desktop_point = desktop_point[:3]
                                
                                if np.all(np.isfinite(desktop_point)):
                                    desktop_points.append(desktop_point)
                            except Exception as e:
                                continue
                
                # 如果没有找到camera_points，尝试其他可能的属性
                elif hasattr(result, 'points_3d_camera') and result.points_3d_camera is not None and len(result.points_3d_camera) > 0:
                    print(f"     使用points_3d_camera属性: {len(result.points_3d_camera)} 个点")
                    camera_points = result.points_3d_camera
                    has_points = True
                    
                    # 将相机坐标系的点转换为桌面坐标系
                    for point in camera_points:
                        if np.all(np.isfinite(point)):  # 确保点坐标有效
                            try:
                                # 使用齐次坐标直接应用变换矩阵
                                homogeneous_point = np.append(point, 1)
                                desktop_point = self.coordinate_transformer.plane_transform_matrix @ homogeneous_point
                                desktop_point = desktop_point[:3]
                                
                                if np.all(np.isfinite(desktop_point)):
                                    desktop_points.append(desktop_point)
                            except Exception as e:
                                continue
                
                # 如果上述都没有，尝试使用desktop_points属性
                elif hasattr(result, 'desktop_points') and result.desktop_points is not None and len(result.desktop_points) > 0:
                    print(f"     使用desktop_points属性: {len(result.desktop_points)} 个点")
                    desktop_points = result.desktop_points
                    has_points = True
                
                # 如果上述都没有，尝试使用points_3d_desktop属性
                elif hasattr(result, 'points_3d_desktop') and result.points_3d_desktop is not None and len(result.points_3d_desktop) > 0:
                    print(f"     使用points_3d_desktop属性: {len(result.points_3d_desktop)} 个点")
                    desktop_points = result.points_3d_desktop
                    has_points = True
                
                # 创建物体点云
                if desktop_points and len(desktop_points) > 0:
                    print(f"     转换得到 {len(desktop_points)} 个桌面坐标点")
                    
                    # 创建物体点云
                    object_pcd = o3d.geometry.PointCloud()
                    object_pcd.points = o3d.utility.Vector3dVector(np.array(desktop_points))
                    
                    # 为物体分配颜色
                    is_real = True  # 默认为真实物体
                    if hasattr(result, 'is_real_object'):
                        is_real = result.is_real_object
                    
                    if is_real:
                        # 真实物体使用明亮颜色
                        color = object_colors[i % len(object_colors)]
                    else:
                        # 平面物体使用较暗颜色
                        base_color = object_colors[i % len(object_colors)]
                        color = [c * 0.6 for c in base_color]
                    
                    # 为所有点分配相同颜色
                    object_colors_array = np.tile(color, (len(desktop_points), 1))
                    object_pcd.colors = o3d.utility.Vector3dVector(object_colors_array)
                    
                    geometries.append(object_pcd)
                    
                    # ======== 使用预计算的OBB角点创建3D边界框 ========
                    if hasattr(result, 'oriented_bbox_corners_cam') and result.oriented_bbox_corners_cam is not None:
                        bbox_3d = self._create_oriented_bbox_from_corners(result.oriented_bbox_corners_cam, color)
                        if bbox_3d is not None:
                            geometries.append(bbox_3d)
                    else:
                        print(f"     ⚠️ 物体没有预计算的OBB角点，跳过边界框绘制")
                else:
                    print(f"     ⚠️ 没有3D点数据")
            
            # 3. 添加桌面坐标系（Y轴向上为桌面法向量）
            coordinate_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.1, origin=[0, 0, 0])
            geometries.append(coordinate_frame)
            
            if len(geometries) <= 1:  # 只有坐标系
                print("⚠️ 没有生成任何点云数据，无法显示")
                return
            
            # 4. 显示交互式窗口
            print(f"🖼️ 显示交互式3D点云窗口，包含 {len(geometries)} 个几何体...")
            print("🔹 坐标系说明:")
            print("   - X轴（红色）: 水平向右")
            print("   - Y轴（绿色）: 垂直向上（桌面法向量）")
            print("   - Z轴（蓝色）: 水平向前")
            print("🔹 交互控制:")
            print("   - 鼠标左键拖拽: 旋转视角")
            print("   - 鼠标右键拖拽: 平移视角")
            print("   - 鼠标滚轮: 缩放")
            print("   - 按 H 键: 显示帮助")
            print("   - 按 R 键: 重置视角")
            print("   - 按 ESC 键: 关闭窗口")
            
            # 创建可视化窗口
            vis = o3d.visualization.Visualizer()
            vis.create_window(window_name="Interactive 3D Point Cloud - Desktop Coordinate System", 
                            width=1200, height=800)
            
            # 添加几何体
            for geometry in geometries:
                vis.add_geometry(geometry)
            
            # 设置渲染选项
            render_option = vis.get_render_option()
            render_option.show_coordinate_frame = True
            render_option.background_color = np.array([1.0, 1.0, 1.0])  # 白色背景
            render_option.point_size = 5.0  # 增大点云尺寸
            
            # 设置视角（适合桌面坐标系）
            view_control = vis.get_view_control()
            view_control.set_front([0, -0.5, -1])  # 从上方斜向下看
            view_control.set_up([0, 1, 0])         # Y轴向上
            view_control.set_lookat([0, 0, 0])     # 看向原点
            view_control.set_zoom(0.8)
            
            # 运行可视化窗口
            vis.run()
            vis.destroy_window()
            
        except ImportError:
            print("❌ Open3D 未安装，无法显示3D点云")
            print("💡 请安装: pip install open3d")
        except Exception as e:
            print(f"❌ 3D点云显示失败: {e}")
            traceback.print_exc()

    def _create_oriented_bbox_from_corners(self, corners_cam, color):
        """根据相机坐标系下的OBB角点，创建桌面坐标系下的LineSet"""
        try:
            import open3d as o3d

            # 将8个角点从相机坐标系转换到桌面坐标系
            desktop_corners = []
            for corner in corners_cam:
                desktop_corner = self.coordinate_transformer.transform_camera_to_plane_coordinate(np.array(corner))
                if desktop_corner is not None:
                    desktop_corners.append(desktop_corner)
                else:
                    print("⚠️ OBB角点转换失败，无法创建边界框")
                    return None
            
            if len(desktop_corners) != 8:
                return None

            # 定义OBB的12条边
            lines = [
                [0, 1], [0, 2], [1, 3], [2, 3],
                [4, 5], [4, 6], [5, 7], [6, 7],
                [0, 4], [1, 5], [2, 6], [3, 7]
            ]
            
            line_set = o3d.geometry.LineSet(
                points=o3d.utility.Vector3dVector(desktop_corners),
                lines=o3d.utility.Vector2iVector(lines),
            )
            line_set.paint_uniform_color(color)
            return line_set

        except Exception as e:
            print(f"❌ 从角点创建OBB失败: {e}")
            return None

    def visualize_table_detection(self, frames=None):
        """
        可视化桌面检测结果，显示哪些点被识别为"桌面"
        
        Args:
            frames: 相机帧数据
        """
        try:
            import open3d as o3d
            
            print("🎨 创建桌面检测可视化窗口...")
            
            if frames is None:
                print("⚠️ 没有帧数据，无法显示点云")
                return
            
            # 检查是否有双重平面检测结果
            if not hasattr(self.coordinate_transformer, 'dual_plane_result') or \
               self.coordinate_transformer.dual_plane_result is None:
                print("⚠️ 没有双重平面检测结果")
                print("💡 尝试重新检测桌面平面...")
                if not self.coordinate_transformer.auto_detect_table_plane(frames):
                    print("❌ 桌面检测失败")
                    return
            
            dual_result = self.coordinate_transformer.dual_plane_result
            
            # 获取原始点云和桌面内点索引
            all_points = dual_result.get('all_points')
            table_inliers_idx = dual_result.get('table_inliers_idx')
            
            if all_points is None or table_inliers_idx is None:
                print("❌ 缺少必要的点云数据或桌面内点索引")
                return
            
            print(f"📊 可视化数据统计:")
            print(f"   总点数: {len(all_points):,}")
            print(f"   桌面点数: {len(table_inliers_idx):,}")
            print(f"   桌面点比例: {len(table_inliers_idx)/len(all_points)*100:.1f}%")
            
            # 创建几何体列表
            geometries = []
            
            # 1. 创建桌面点云（红色）
            table_points = all_points[table_inliers_idx]
            table_pcd = o3d.geometry.PointCloud()
            table_pcd.points = o3d.utility.Vector3dVector(table_points / 1000.0)  # 转换为米
            table_colors = np.tile([1.0, 0.0, 0.0], (len(table_points), 1))  # 红色
            table_pcd.colors = o3d.utility.Vector3dVector(table_colors)
            geometries.append(table_pcd)
            
            # 2. 创建非桌面点云（灰色，采样显示）
            non_table_indices = np.setdiff1d(np.arange(len(all_points)), table_inliers_idx)
            if len(non_table_indices) > 0:
                # 对非桌面点进行采样以减少显示负担
                max_non_table_points = 5000
                if len(non_table_indices) > max_non_table_points:
                    sampled_indices = np.random.choice(non_table_indices, max_non_table_points, replace=False)
                else:
                    sampled_indices = non_table_indices
                
                non_table_points = all_points[sampled_indices]
                non_table_pcd = o3d.geometry.PointCloud()
                non_table_pcd.points = o3d.utility.Vector3dVector(non_table_points / 1000.0)  # 转换为米
                non_table_colors = np.tile([0.7, 0.7, 0.7], (len(non_table_points), 1))  # 灰色
                non_table_pcd.colors = o3d.utility.Vector3dVector(non_table_colors)
                geometries.append(non_table_pcd)
                
                print(f"   非桌面点（采样显示）: {len(sampled_indices):,} / {len(non_table_indices):,}")
            
            # 3. 添加桌面平面（如果有变换矩阵）
            if self.coordinate_transformer.plane_transform_matrix is not None:
                # 获取桌面信息
                table_info = dual_result.get('table', {})
                table_center = table_info.get('center')
                table_normal = table_info.get('normal')
                
                if table_center is not None and table_normal is not None:
                    # 创建桌面平面的可视化
                    plane_mesh = self._create_plane_mesh(table_center, table_normal, size=0.5)
                    if plane_mesh is not None:
                        geometries.append(plane_mesh)
            
            # 4. 添加坐标系
            coordinate_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.1, origin=[0, 0, 0])
            geometries.append(coordinate_frame)
            
            if len(geometries) <= 1:  # 只有坐标系
                print("⚠️ 没有生成任何点云数据，无法显示")
                return
            
            # 5. 显示交互式窗口
            print(f"🖼️ 显示桌面检测结果，包含 {len(geometries)} 个几何体...")
            print("🔹 颜色说明:")
            print("   - 红色: 被识别为桌面的点")
            print("   - 灰色: 非桌面点（采样显示）")
            print("   - 绿色半透明: 检测到的桌面平面")
            print("🔹 交互控制:")
            print("   - 鼠标左键拖拽: 旋转视角")
            print("   - 鼠标右键拖拽: 平移视角")
            print("   - 鼠标滚轮: 缩放")
            print("   - 按 ESC 键: 关闭窗口")
            
            # 创建可视化窗口
            vis = o3d.visualization.Visualizer()
            vis.create_window(window_name="Table Detection Visualization", 
                            width=1200, height=800)
            
            # 添加几何体
            for geometry in geometries:
                vis.add_geometry(geometry)
            
            # 设置渲染选项
            render_option = vis.get_render_option()
            render_option.show_coordinate_frame = True
            render_option.background_color = np.array([0.1, 0.1, 0.1])  # 深色背景
            render_option.point_size = 3.0
            
            # 设置视角
            view_control = vis.get_view_control()
            view_control.set_front([0, -0.7, -0.7])  # 从上方斜向下看
            view_control.set_up([0, 1, 0])           # Y轴向上
            view_control.set_lookat([0, 0, 0])       # 看向原点
            view_control.set_zoom(0.8)
            
            # 运行可视化窗口
            vis.run()
            vis.destroy_window()
            
        except ImportError:
            print("❌ Open3D 未安装，无法显示3D点云")
            print("💡 请安装: pip install open3d")
        except Exception as e:
            print(f"❌ 桌面检测可视化失败: {e}")
            traceback.print_exc()

    def _create_plane_mesh(self, center, normal, size=0.5, color=[0.0, 1.0, 0.0, 0.3]):
        """
        创建平面网格用于可视化
        
        Args:
            center: 平面中心点（毫米）
            normal: 平面法向量
            size: 平面大小（米）
            color: 颜色 [R, G, B, A]
            
        Returns:
            Open3D TriangleMesh 对象
        """
        try:
            import open3d as o3d
            
            # 转换中心点为米
            center_m = np.array(center) / 1000.0
            normal = np.array(normal)
            normal = normal / np.linalg.norm(normal)  # 归一化
            
            # 创建两个垂直于法向量的向量
            if abs(normal[0]) < 0.9:
                u = np.cross(normal, [1, 0, 0])
            else:
                u = np.cross(normal, [0, 1, 0])
            u = u / np.linalg.norm(u)
            v = np.cross(normal, u)
            v = v / np.linalg.norm(v)
            
            # 创建平面的四个顶点
            vertices = [
                center_m + size * (-u - v),
                center_m + size * (u - v),
                center_m + size * (u + v),
                center_m + size * (-u + v)
            ]
            
            # 创建三角形面
            triangles = [
                [0, 1, 2],
                [0, 2, 3]
            ]
            
            # 创建网格
            mesh = o3d.geometry.TriangleMesh()
            mesh.vertices = o3d.utility.Vector3dVector(vertices)
            mesh.triangles = o3d.utility.Vector3iVector(triangles)
            mesh.paint_uniform_color(color[:3])  # 只使用RGB，忽略透明度
            
            # 计算法向量
            mesh.compute_vertex_normals()
            
            return mesh
            
        except Exception as e:
            print(f"❌ 创建平面网格失败: {e}")
            return None