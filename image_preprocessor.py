#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像预处理模块
提供直方图均衡化和超分辨率功能，用于改善YOLO检测效果
"""

import numpy as np
import cv2
from typing import Optional, Tuple, Dict, Any
import os
import time
from enum import Enum

# 导入配置模块
from config import SystemConfig, ImagePreprocessingConfig

class SuperResolutionMethod(Enum):
    """超分辨率方法枚举"""
    BICUBIC = "bicubic"
    LANCZOS = "lanczos"
    INTER_CUBIC = "inter_cubic"
    INTER_LINEAR = "inter_linear"
    INTER_NEAREST = "inter_nearest"

class ImagePreprocessor:
    """图像预处理器"""
    
    def __init__(self, config: SystemConfig = None):
        """
        初始化图像预处理器
        
        Args:
            config: 系统配置对象
        """
        self.config = config
        self.preprocessing_config = config.image_preprocessing if config else None
        self.original_size = None
        self.processed_size = None
        
        # 如果没有提供配置，使用默认配置
        if self.preprocessing_config is None:
            self.preprocessing_config = ImagePreprocessingConfig()
    
    def preprocess_image(self, image: np.ndarray, 
                        return_original_size: bool = False) -> Tuple[np.ndarray, Optional[Dict[str, Any]]]:
        """
        对图像进行完整的预处理
        
        Args:
            image: 输入图像 (BGR格式)
            return_original_size: 是否返回原始尺寸信息
            
        Returns:
            预处理后的图像和可选的原始尺寸信息
        """
        if image is None:
            return None, None
            
        # 记录原始尺寸
        self.original_size = image.shape[:2]  # (height, width)
        
        # 创建图像副本
        processed_image = image.copy()
        
        # 1. 直方图均衡化
        if self.preprocessing_config.histogram_equalization_enabled:
            processed_image = self._apply_histogram_equalization(processed_image)
        
        # 2. 降噪处理
        if self.preprocessing_config.noise_reduction_enabled:
            processed_image = self._apply_noise_reduction(processed_image)
        
        # 3. 锐化处理
        if self.preprocessing_config.sharpening_enabled:
            processed_image = self._apply_sharpening(processed_image)
        
        # 4. 超分辨率
        if self.preprocessing_config.super_resolution_enabled:
            processed_image = self._apply_super_resolution(processed_image)
            self.processed_size = processed_image.shape[:2]
        
        # 准备返回信息
        info = None
        if return_original_size:
            info = {
                'original_size': self.original_size,
                'processed_size': self.processed_size,
                'scale_factor': self.preprocessing_config.scale_factor if self.preprocessing_config.super_resolution_enabled else 1.0
            }
        
        return processed_image, info
    
    def _apply_histogram_equalization(self, image: np.ndarray) -> np.ndarray:
        """
        应用直方图均衡化
        
        Args:
            image: 输入图像
            
        Returns:
            均衡化后的图像
        """
        method = self.preprocessing_config.histogram_equalization_method
        
        if method == "clahe":
            # 使用CLAHE (Contrast Limited Adaptive Histogram Equalization)
            clahe = cv2.createCLAHE(
                clipLimit=self.preprocessing_config.clahe_clip_limit,
                tileGridSize=self.preprocessing_config.clahe_tile_grid_size
            )
            
            # 转换到LAB色彩空间进行CLAHE
            lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
            lab[:, :, 0] = clahe.apply(lab[:, :, 0])  # 只对亮度通道应用CLAHE
            enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
            
        elif method == "equalize":
            # 使用全局直方图均衡化
            yuv = cv2.cvtColor(image, cv2.COLOR_BGR2YUV)
            yuv[:, :, 0] = cv2.equalizeHist(yuv[:, :, 0])
            enhanced = cv2.cvtColor(yuv, cv2.COLOR_YUV2BGR)
        
        else:
            enhanced = image
        
        return enhanced
    
    def _apply_noise_reduction(self, image: np.ndarray) -> np.ndarray:
        """
        应用降噪处理
        
        Args:
            image: 输入图像
            
        Returns:
            降噪后的图像
        """
        method = self.preprocessing_config.noise_reduction_method
        
        if method == "bilateral":
            return cv2.bilateralFilter(
                image, 
                self.preprocessing_config.bilateral_d,
                self.preprocessing_config.bilateral_sigma_color,
                self.preprocessing_config.bilateral_sigma_space
            )
        elif method == "gaussian":
            return cv2.GaussianBlur(
                image, 
                self.preprocessing_config.gaussian_ksize,
                self.preprocessing_config.gaussian_sigma_x
            )
        elif method == "median":
            return cv2.medianBlur(image, self.preprocessing_config.median_ksize)
        else:
            return image
    
    def _apply_sharpening(self, image: np.ndarray) -> np.ndarray:
        """
        应用锐化处理
        
        Args:
            image: 输入图像
            
        Returns:
            锐化后的图像
        """
        if self.preprocessing_config.sharpening_method == "unsharp_mask":
            # 使用非锐化掩码
            blurred = cv2.GaussianBlur(
                image, 
                self.preprocessing_config.sharpening_kernel_size, 
                self.preprocessing_config.sharpening_sigma
            )
            sharpened = cv2.addWeighted(
                image, 
                1 + self.preprocessing_config.sharpening_amount, 
                blurred, 
                -self.preprocessing_config.sharpening_amount, 
                self.preprocessing_config.sharpening_threshold
            )
            return np.clip(sharpened, 0, 255).astype(np.uint8)
        else:
            return image
    
    def _apply_super_resolution(self, image: np.ndarray) -> np.ndarray:
        """
        应用超分辨率处理
        
        Args:
            image: 输入图像
            
        Returns:
            超分辨率后的图像
        """
        target_width = self.preprocessing_config.target_width
        target_height = self.preprocessing_config.target_height
        method = self.preprocessing_config.super_resolution_method
        
        # 选择插值方法
        if method == "bicubic":
            interpolation = cv2.INTER_CUBIC
        elif method == "lanczos":
            interpolation = cv2.INTER_LANCZOS4
        elif method == "inter_cubic":
            interpolation = cv2.INTER_CUBIC
        elif method == "inter_linear":
            interpolation = cv2.INTER_LINEAR
        elif method == "inter_nearest":
            interpolation = cv2.INTER_NEAREST
        else:
            interpolation = cv2.INTER_CUBIC
        
        # 执行超分辨率
        resized = cv2.resize(image, (target_width, target_height), interpolation=interpolation)
        
        return resized
    
    def adjust_bbox_for_super_resolution(self, bbox: Tuple[int, int, int, int], 
                                       scale_factor: float = None) -> Tuple[int, int, int, int]:
        """
        调整边界框以适应超分辨率后的图像
        
        Args:
            bbox: 原始边界框 (x1, y1, x2, y2)
            scale_factor: 缩放因子，如果为None则使用配置中的值
            
        Returns:
            调整后的边界框
        """
        if scale_factor is None:
            scale_factor = self.preprocessing_config.scale_factor
            
        x1, y1, x2, y2 = bbox
        adjusted_bbox = (
            int(x1 * scale_factor),
            int(y1 * scale_factor),
            int(x2 * scale_factor),
            int(y2 * scale_factor)
        )
        return adjusted_bbox
    
    def adjust_mask_for_super_resolution(self, mask: np.ndarray, 
                                       target_size: Tuple[int, int] = None) -> np.ndarray:
        """
        调整分割掩码以适应超分辨率后的图像
        
        Args:
            mask: 原始掩码
            target_size: 目标尺寸 (width, height)，如果为None则使用配置中的值
            
        Returns:
            调整后的掩码
        """
        if mask is None:
            return None
        
        if target_size is None:
            target_size = (self.preprocessing_config.target_width, self.preprocessing_config.target_height)
        
        # 使用最近邻插值保持掩码的二进制特性
        resized_mask = cv2.resize(mask, target_size, interpolation=cv2.INTER_NEAREST)
        
        # 确保掩码是二进制的
        resized_mask = (resized_mask > 0.5).astype(np.uint8)
        
        return resized_mask