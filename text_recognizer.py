#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文字识别模块
基于PaddleOCR实现高分辨率图像中的文字识别功能
"""

import cv2
import numpy as np
import os
from typing import List, Tuple, Optional, Dict, Any
from dataclasses import dataclass
import time
import tempfile
import contextlib

# 导入PaddleOCR相关模块
try:
    from paddlex import create_model
    print("✅ PaddleX库导入成功")
except ImportError as e:
    print(f"❌ 导入PaddleX失败: {e}")
    print("💡 请确保已正确安装PaddleX库")
    raise

from affine_mapper import MappedRegion


@dataclass
class TextResult:
    """文字识别结果"""
    text: str
    confidence: float
    bbox: Tuple[int, int, int, int]  # 在高分辨率图像中的位置
    region_name: str  # 对应的物体类别名称
    region_confidence: float  # 物体检测的置信度


class TextRecognizer:
    """
    文字识别器
    负责在高分辨率图像的指定区域内进行文字识别
    """

    def __init__(self, rec_model_name: str = 'PP-OCRv4_server_rec',
                 det_model_name: str = 'PP-OCRv4_server_det',
                 affine_mapper=None,
                 confidence_threshold: float = 0.2):
        """
        初始化文字识别器

        Args:
            rec_model_name: 识别模型名称 (可选: PP-OCRv5_server_rec, PP-OCRv4_server_rec等)
            det_model_name: 检测模型名称 (可选: PP-OCRv5_server_det, PP-OCRv4_server_det等)
            affine_mapper: 仿射变换映射器实例 (可选，用于减少外部依赖)
            confidence_threshold: 文字识别置信度阈值
        """
        self.rec_model_name = rec_model_name
        self.det_model_name = det_model_name
        self.rec_model = None
        self.det_model = None
        self.affine_mapper = affine_mapper
        self.confidence_threshold = confidence_threshold
        self.temp_dir = "temp_text_regions"

        # 创建临时目录
        os.makedirs(self.temp_dir, exist_ok=True)

        # 颜色映射表（避免每次重新计算）
        self.color_map = [
            (0, 255, 0),    # 绿色
            (255, 0, 0),    # 蓝色
            (0, 0, 255),    # 红色
            (255, 255, 0),  # 青色
            (255, 0, 255),  # 品红色
            (0, 255, 255),  # 黄色
            (128, 0, 128),  # 紫色
            (255, 165, 0),  # 橙色
        ]

        self.initialize_models()

        print("📝 文字识别器初始化完成")
        print(f"   检测模型: {self.det_model_name}")
        print(f"   识别模型: {self.rec_model_name}")
        print(f"   置信度阈值: {self.confidence_threshold}")
    
    def initialize_models(self):
        """初始化PaddleOCR检测和识别模型"""
        try:
            # 初始化检测模型
            print(f"🔧 正在加载文字检测模型: {self.det_model_name}")
            self.det_model = create_model(model_name=self.det_model_name)
            print("✅ 文字检测模型加载成功")
            
            # 初始化识别模型
            print(f"🔧 正在加载文字识别模型: {self.rec_model_name}")
            self.rec_model = create_model(model_name=self.rec_model_name)
            print("✅ 文字识别模型加载成功")
            
        except Exception as e:
            print(f"❌ 文字模型加载失败: {e}")
            raise
    
    def preprocess_roi_for_ocr(self, roi_image: np.ndarray) -> np.ndarray:
        """
        对ROI图像进行OCR预处理，针对中文优化
        
        Args:
            roi_image: 输入的ROI图像
            
        Returns:
            预处理后的图像
        """
        try:
            # 对于中文识别，通常保持原始彩色图像效果更好
            # 如果图像太暗或太亮，进行简单的对比度调整
            if len(roi_image.shape) == 3:
                # 计算图像亮度
                gray = cv2.cvtColor(roi_image, cv2.COLOR_BGR2GRAY)
                mean_brightness = np.mean(gray)
                
                # 如果图像过暗或过亮，进行gamma校正
                if mean_brightness < 100:  # 图像过暗
                    gamma = 1.5
                    roi_image = np.power(roi_image / 255.0, 1.0 / gamma) * 255.0
                    roi_image = roi_image.astype(np.uint8)
                elif mean_brightness > 200:  # 图像过亮
                    gamma = 0.7
                    roi_image = np.power(roi_image / 255.0, 1.0 / gamma) * 255.0
                    roi_image = roi_image.astype(np.uint8)
                
                # 轻微的高斯模糊去噪
                roi_image = cv2.GaussianBlur(roi_image, (3, 3), 0)
            
            return roi_image
            
        except Exception as e:
            print(f"⚠️ ROI预处理失败: {e}")
            return roi_image
    
    def detect_text_in_roi(self, roi_image: np.ndarray, roi_id: int = 0) -> List[Dict]:
        """
        在ROI区域中检测文字位置
        
        Args:
            roi_image: ROI图像
            roi_id: ROI编号
            
        Returns:
            检测到的文字区域列表
        """
        text_regions = []
        
        try:
            if roi_image is None or roi_image.size == 0:
                print(f"⚠️ ROI {roi_id} 图像无效")
                return text_regions
            
            # 检查图像尺寸
            h, w = roi_image.shape[:2]
            if h < 10 or w < 10:
                print(f"⚠️ ROI {roi_id} 尺寸太小: {w}x{h}")
                return text_regions
            
            print(f"🔍 开始检测ROI {roi_id} 中的文字区域 (尺寸: {w}x{h})")
            
            # 使用安全的临时文件
            with tempfile.NamedTemporaryFile(suffix='.png', dir=self.temp_dir, delete=False) as tmp_file:
                temp_path = tmp_file.name
                cv2.imwrite(temp_path, roi_image)

            try:
                # 进行文字检测
                det_output_generator = self.det_model.predict(input=temp_path, batch_size=1)
                det_output = list(det_output_generator)

                # 处理检测结果
                for det_res in det_output:
                    # 获取检测到的多边形和分数
                    if isinstance(det_res, dict):
                        dt_polys = det_res.get('dt_polys', [])
                        dt_scores = det_res.get('dt_scores', [])
                    elif hasattr(det_res, 'dt_polys'):
                        dt_polys = det_res.dt_polys
                        dt_scores = det_res.dt_scores if hasattr(det_res, 'dt_scores') else []
                    else:
                        continue
                    
                    if dt_polys is None:
                        continue
                    
                    print(f"   🔍 检测到 {len(dt_polys)} 个文字区域")
                    
                    # 处理每个检测到的文字区域
                    for j, poly in enumerate(dt_polys):
                        score = dt_scores[j] if j < len(dt_scores) else 0.0
                        
                        # 将多边形转换为边界框
                        poly_array = np.array(poly, dtype=np.int32)
                        x_min = int(np.min(poly_array[:, 0]))
                        y_min = int(np.min(poly_array[:, 1]))
                        x_max = int(np.max(poly_array[:, 0]))
                        y_max = int(np.max(poly_array[:, 1]))
                        
                        # 确保坐标在图像范围内
                        x_min = max(0, x_min)
                        y_min = max(0, y_min)
                        x_max = min(w, x_max)
                        y_max = min(h, y_max)
                        
                        # 检查区域大小
                        if x_max > x_min and y_max > y_min:
                            # 裁剪文字区域
                            text_region_image = roi_image[y_min:y_max, x_min:x_max]
                            
                            text_region = {
                                'image': text_region_image,
                                'bbox': [x_min, y_min, x_max, y_max],
                                'poly': poly,
                                'score': score,
                                'region_id': f"{roi_id}_{j}"
                            }
                            text_regions.append(text_region)
                            
                            print(f"     区域 {j+1}: {x_min},{y_min},{x_max},{y_max} (检测置信度: {score:.3f})")
                
            finally:
                # 删除临时文件
                if os.path.exists(temp_path):
                    try:
                        os.remove(temp_path)
                    except:
                        pass
            
            if not text_regions:
                print(f"   ❌ ROI {roi_id} 中未检测到文字区域")
            else:
                print(f"   ✅ ROI {roi_id} 检测完成，共 {len(text_regions)} 个文字区域")
                
        except Exception as e:
            print(f"❌ ROI {roi_id} 文字检测失败: {e}")
            import traceback
            traceback.print_exc()
        
        return text_regions
    
    def recognize_text_in_roi(self, roi_image: np.ndarray, region_info: MappedRegion, 
                             roi_id: int = 0) -> List[TextResult]:
        """
        在ROI区域中先检测再识别文字
        
        Args:
            roi_image: ROI图像
            region_info: 映射区域信息
            roi_id: ROI编号
            
        Returns:
            文字识别结果列表
        """
        results = []
        
        try:
            if roi_image is None or roi_image.size == 0:
                print(f"⚠️ ROI {roi_id} 图像无效")
                return results
            
            # 检查图像尺寸
            h, w = roi_image.shape[:2]
            if h < 10 or w < 10:
                print(f"⚠️ ROI {roi_id} 尺寸太小: {w}x{h}")
                return results
            
            print(f"📝 开始处理ROI {roi_id} (尺寸: {w}x{h})")
            
            # 第一步：检测文字区域
            text_regions = self.detect_text_in_roi(roi_image, roi_id)
            
            if not text_regions:
                print(f"   ❌ ROI {roi_id} 中未检测到文字区域")
                return results
            
            # 第二步：对每个检测到的文字区域进行识别
            print(f"📝 开始识别 {len(text_regions)} 个文字区域...")
            
            for i, text_region in enumerate(text_regions):
                try:
                    region_image = text_region['image']
                    region_bbox = text_region['bbox']
                    detection_score = text_region['score']
                    
                    # 检查文字区域图像
                    if region_image is None or region_image.size == 0:
                        print(f"   ⚠️ 文字区域 {i+1} 图像无效")
                        continue
                    
                    region_h, region_w = region_image.shape[:2]
                    if region_h < 5 or region_w < 5:
                        print(f"   ⚠️ 文字区域 {i+1} 太小: {region_w}x{region_h}")
                        continue
                    
                    print(f"   📝 识别文字区域 {i+1}/{len(text_regions)} (尺寸: {region_w}x{region_h})")
                    
                    # 预处理图像
                    processed_region = self.preprocess_roi_for_ocr(region_image)
                    
                    # 使用安全的临时文件
                    with tempfile.NamedTemporaryFile(suffix='.png', dir=self.temp_dir, delete=False) as tmp_file:
                        temp_path = tmp_file.name
                        cv2.imwrite(temp_path, processed_region)

                    try:
                        # 进行文字识别
                        rec_output_generator = self.rec_model.predict(input=temp_path, batch_size=1)
                        rec_output = list(rec_output_generator)
                        
                        # 处理识别结果
                        for rec_res in rec_output:
                            # 检查识别结果的类型和内容
                            if isinstance(rec_res, dict):
                                rec_text = rec_res.get('rec_text', '')
                                rec_score = rec_res.get('rec_score', 0.0)
                            elif hasattr(rec_res, 'rec_text'):
                                rec_text = rec_res.rec_text
                                rec_score = rec_res.rec_score if hasattr(rec_res, 'rec_score') else 0.0
                            else:
                                print(f"     ⚠️ 未知的识别结果格式: {type(rec_res)}")
                                continue
                            
                            # 过滤有效文本
                            if rec_text and rec_text.strip() and len(rec_text.strip()) > 0:
                                # 过滤置信度过低的结果
                                if rec_score >= self.confidence_threshold:
                                    # 计算在原ROI中的绝对坐标
                                    abs_x1 = region_info.bbox[0] + region_bbox[0]
                                    abs_y1 = region_info.bbox[1] + region_bbox[1]
                                    abs_x2 = region_info.bbox[0] + region_bbox[2]
                                    abs_y2 = region_info.bbox[1] + region_bbox[3]
                                    
                                    text_result = TextResult(
                                        text=rec_text.strip(),
                                        confidence=rec_score,
                                        bbox=(abs_x1, abs_y1, abs_x2, abs_y2),
                                        region_name=region_info.class_name,
                                        region_confidence=region_info.confidence
                                    )
                                    results.append(text_result)
                                    
                                    print(f"     ✅ 识别到文字: '{rec_text.strip()}' (识别置信度: {rec_score:.3f}, 检测置信度: {detection_score:.3f})")
                                else:
                                    print(f"     ⚠️ 文字置信度过低: '{rec_text.strip()}' (置信度: {rec_score:.3f})")
                            else:
                                print(f"     ⚠️ 识别结果为空或无效")
                    
                    finally:
                        # 删除临时文件
                        if os.path.exists(temp_path):
                            try:
                                os.remove(temp_path)
                            except:
                                pass
                
                except Exception as e:
                    print(f"   ❌ 处理文字区域 {i+1} 失败: {e}")
                    continue
            
            if not results:
                print(f"   ❌ ROI {roi_id} 中未识别到有效文字")
            else:
                print(f"   ✅ ROI {roi_id} 识别完成，共 {len(results)} 个文字结果")
            
        except Exception as e:
            print(f"❌ ROI {roi_id} 文字识别失败: {e}")
            import traceback
            traceback.print_exc()
        
        return results
    
    def recognize_text_in_mapped_regions(self, high_res_image: np.ndarray,
                                       mapped_regions: List[MappedRegion],
                                       affine_mapper=None) -> List[TextResult]:
        """
        在所有映射区域中批量识别文字

        Args:
            high_res_image: 1920×1080高分辨率图像
            mapped_regions: 映射后的区域列表
            affine_mapper: 仿射变换映射器实例 (可选，如果未提供则使用初始化时的实例)

        Returns:
            所有文字识别结果
        """
        all_text_results = []

        # 使用传入的mapper或初始化时的mapper
        mapper = affine_mapper or self.affine_mapper
        if mapper is None:
            print("❌ 未提供仿射变换映射器，无法处理映射区域")
            return all_text_results

        print(f"\n📝 开始在 {len(mapped_regions)} 个映射区域中识别文字...")

        for i, mapped_region in enumerate(mapped_regions):
            try:
                print(f"\n--- 处理第 {i+1}/{len(mapped_regions)} 个区域 ---")
                print(f"物体类别: {mapped_region.class_name}")
                print(f"物体置信度: {mapped_region.confidence:.3f}")
                print(f"映射边界框: {mapped_region.bbox}")

                # 从高分辨率图像中提取ROI
                roi_image, adjusted_bbox = mapper.create_roi_from_mapped_region(
                    high_res_image, mapped_region, padding=15
                )
                
                if roi_image is None:
                    print(f"⚠️ 无法提取第 {i+1} 个区域的ROI")
                    continue
                
                # 在ROI中识别文字
                text_results = self.recognize_text_in_roi(roi_image, mapped_region, roi_id=i+1)
                
                # 添加到总结果中
                all_text_results.extend(text_results)
                
            except Exception as e:
                print(f"❌ 处理第 {i+1} 个映射区域失败: {e}")
                continue
        
        print(f"\n📊 文字识别总结:")
        print(f"   处理区域数: {len(mapped_regions)}")
        print(f"   识别到文字结果数: {len(all_text_results)}")
        
        if all_text_results:
            print(f"\n📝 所有识别到的文字:")
            for i, result in enumerate(all_text_results, 1):
                print(f"   {i}. '{result.text}' (置信度: {result.confidence:.3f}, "
                      f"来自: {result.region_name})")
        
        return all_text_results
    
    def save_text_results(self, text_results: List[TextResult], 
                         output_file: str = None) -> str:
        """
        保存文字识别结果到文件
        
        Args:
            text_results: 文字识别结果列表
            output_file: 输出文件路径
            
        Returns:
            保存的文件路径
        """
        try:
            if output_file is None:
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                output_file = f"output/text_recognition_results_{timestamp}.txt"
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("文字识别结果报告\n")
                f.write("=" * 50 + "\n")
                f.write(f"识别时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"识别模型: {self.rec_model_name}\n")
                f.write(f"总结果数: {len(text_results)}\n\n")
                
                if text_results:
                    f.write("详细结果:\n")
                    f.write("-" * 30 + "\n")
                    
                    for i, result in enumerate(text_results, 1):
                        f.write(f"{i}. 文字内容: {result.text}\n")
                        f.write(f"   识别置信度: {result.confidence:.4f}\n")
                        f.write(f"   位置(高分辨率): {result.bbox}\n")
                        f.write(f"   来源物体: {result.region_name}\n")
                        f.write(f"   物体置信度: {result.region_confidence:.4f}\n")
                        f.write("\n")
                    
                    # 按物体分组统计
                    f.write("\n按物体分组统计:\n")
                    f.write("-" * 30 + "\n")
                    
                    region_texts = {}
                    for result in text_results:
                        if result.region_name not in region_texts:
                            region_texts[result.region_name] = []
                        region_texts[result.region_name].append(result.text)
                    
                    for region_name, texts in region_texts.items():
                        f.write(f"{region_name}: {', '.join(texts)}\n")
                
                else:
                    f.write("未识别到任何文字内容。\n")
            
            print(f"✅ 文字识别结果已保存到: {output_file}")
            return output_file
            
        except Exception as e:
            print(f"❌ 保存文字识别结果失败: {e}")
            return ""
    

    def cleanup(self):
        """清理临时文件"""
        try:
            if os.path.exists(self.temp_dir):
                import shutil
                shutil.rmtree(self.temp_dir)
                print("✅ 文字识别临时文件清理完成")
        except Exception as e:
            print(f"⚠️ 清理临时文件失败: {e}")

    def __del__(self):
        """析构函数，自动清理临时文件"""
        try:
            self.cleanup()
        except:
            pass

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，自动清理"""
        self.cleanup()

    def detect_text(self, roi_image: np.ndarray, roi_id: int = 0) -> List[Dict]:
        """
        单独的文字检测方法

        Args:
            roi_image: ROI图像
            roi_id: ROI编号

        Returns:
            检测到的文字区域列表
        """
        return self.detect_text_in_roi(roi_image, roi_id)

    def recognize_text(self, text_region_image: np.ndarray, region_id: str = "0") -> Optional[str]:
        """
        单独的文字识别方法

        Args:
            text_region_image: 文字区域图像
            region_id: 区域ID

        Returns:
            识别的文字内容
        """
        try:
            if text_region_image is None or text_region_image.size == 0:
                return None

            # 预处理图像
            processed_region = self.preprocess_roi_for_ocr(text_region_image)

            # 使用安全的临时文件
            with tempfile.NamedTemporaryFile(suffix='.png', dir=self.temp_dir, delete=False) as tmp_file:
                temp_path = tmp_file.name
                cv2.imwrite(temp_path, processed_region)

            try:
                # 进行文字识别
                rec_output_generator = self.rec_model.predict(input=temp_path, batch_size=1)
                rec_output = list(rec_output_generator)

                # 处理识别结果
                for rec_res in rec_output:
                    if isinstance(rec_res, dict):
                        rec_text = rec_res.get('rec_text', '')
                        rec_score = rec_res.get('rec_score', 0.0)
                    elif hasattr(rec_res, 'rec_text'):
                        rec_text = rec_res.rec_text
                        rec_score = rec_res.rec_score if hasattr(rec_res, 'rec_score') else 0.0
                    else:
                        continue

                    # 过滤有效文本
                    if rec_text and rec_text.strip() and rec_score >= self.confidence_threshold:
                        return rec_text.strip()

                return None

            finally:
                # 删除临时文件
                if os.path.exists(temp_path):
                    try:
                        os.remove(temp_path)
                    except:
                        pass

        except Exception as e:
            print(f"❌ 文字识别失败: {e}")
            return None

    def set_confidence_threshold(self, threshold: float):
        """设置置信度阈值"""
        if 0.0 <= threshold <= 1.0:
            self.confidence_threshold = threshold
            print(f"✅ 置信度阈值已设置为: {threshold}")
        else:
            print(f"❌ 无效的置信度阈值: {threshold}，应在0.0-1.0之间")

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'detection_model': self.det_model_name,
            'recognition_model': self.rec_model_name,
            'det_model_loaded': self.det_model is not None,
            'rec_model_loaded': self.rec_model is not None,
            'temp_dir': self.temp_dir,
            'confidence_threshold': self.confidence_threshold,
            'affine_mapper_available': self.affine_mapper is not None
        }