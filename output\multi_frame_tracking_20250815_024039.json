{"timestamp": "2025-08-15T02:40:39.845141", "stats": {"frame_count": 1, "total_tracked_objects": 5, "stable_objects": 0, "class_counts": {"banana": 1, "hanger": 1, "bottle": 1, "can": 1, "dining table": 1}, "stable_class_counts": {}, "fusion_method": "majority_vote", "n_frames": 3, "stable_threshold": 2}, "tracked_objects": {"c2fc60c2": {"class_name": "banana", "class_id": 1, "consecutive_frames": 1, "is_stable": false, "last_seen_frame": 0, "detection_count": 1}, "f8209c4f": {"class_name": "hanger", "class_id": 13, "consecutive_frames": 1, "is_stable": false, "last_seen_frame": 0, "detection_count": 1}, "4085ed09": {"class_name": "bottle", "class_id": 0, "consecutive_frames": 1, "is_stable": false, "last_seen_frame": 0, "detection_count": 1}, "389f7310": {"class_name": "can", "class_id": 10, "consecutive_frames": 1, "is_stable": false, "last_seen_frame": 0, "detection_count": 1}, "645e86a3": {"class_name": "dining table", "class_id": 3, "consecutive_frames": 1, "is_stable": false, "last_seen_frame": 0, "detection_count": 1}}}