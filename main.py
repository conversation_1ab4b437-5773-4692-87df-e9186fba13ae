#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO点云联合检测主程序
整合YOLO物体检测、坐标转换和物体分析功能
"""

import sys
import os
import time
import numpy as np
import cv2
from typing import List, Optional, Tuple
from datetime import datetime

# 导入自定义模块
from config import SystemConfig, default_config
from yolo_detector import YOLODetector, Detection
from multi_frame_yolo_detector import MultiFrameYOLODetector
from coordinate_transformer import CoordinateTransformer
from object_analyzer import ObjectAnalyzer, ObjectAnalysisResult
from pointcloud_visualizer import PointCloudVisualizer
from image_processor import ImageProcessor
from image_preprocessor import ImagePreprocessor
from result_display import ResultDisplay
from config import PathConfig
from camera_controller import CameraController
from object_output import ObjectOutputGenerator
from affine_mapper import AffineMapper, MappedRegion
from text_recognizer import TextRecognizer, TextResult
from pyorbbecsdk import OBFormat, FrameSet


# 在所有模块导入后，但在任何操作开始前，设置并创建所有输出目录
PathConfig.setup_directories()

class YOLOPointCloudSystem:
    """YOLO点云联合检测系统"""
    
    def __init__(self, config: SystemConfig = None):
        """
        初始化系统
        
        Args:
            config: 系统配置
        """
        if config is None:
            config = default_config
        
        self.config = config
        
        print("🚀 初始化YOLO点云联合检测系统...")
        
        # 初始化组件
        self.yolo_detector = None
        self.multi_frame_detector = None  # 新增多帧检测器
        self.coordinate_transformer = None
        self.object_analyzer = None
        self.pointcloud_visualizer = None
        self.image_processor = None
        self.image_preprocessor = None
        self.camera_controller = None
        self.output_generator = None
        self.affine_mapper = None
        self.text_recognizer = None

        # 多帧检测配置（从配置文件读取）
        self.use_multi_frame_detection = config.multi_frame_detection.enabled
        
        self.is_initialized = False
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 统计信息
        self.frame_count = 0
        self.detection_count = 0
        self.analysis_count = 0
    
    def initialize(self):
        """初始化所有组件"""
        try:
            print("🔧 正在初始化系统组件...")
            
            # 1. 初始化相机控制器
            if self.config and hasattr(self.config, 'camera'):
                self.camera_controller = CameraController(self.config.camera)
                print("✅ 相机控制器初始化完成")
            
            # 2. 初始化YOLO检测器
            self.yolo_detector = YOLODetector(self.config)

            # 2.1 初始化多帧YOLO检测器（如果启用）
            if self.use_multi_frame_detection:
                print("🎯 初始化多帧YOLO检测器（低阈值输入，高阈值输出）...")
                mf_config = self.config.multi_frame_detection
                self.multi_frame_detector = MultiFrameYOLODetector(
                    config=self.config,
                    n_frames=mf_config.n_frames,
                    stable_threshold=mf_config.stable_threshold,
                    input_confidence_threshold=mf_config.input_confidence_threshold,
                    output_confidence_threshold=mf_config.output_confidence_threshold,
                    iou_match_threshold=mf_config.iou_match_threshold,
                    fusion_method=mf_config.fusion_method
                )
                # 设置额外参数
                self.multi_frame_detector.max_missing_frames = mf_config.max_missing_frames
                self.multi_frame_detector.iou_stability_threshold = mf_config.iou_stability_threshold
                self.multi_frame_detector.enable_iou_stability_check = mf_config.enable_iou_stability_check
                print("✅ 多帧YOLO检测器初始化完成")
            else:
                print("⚠️ 多帧检测功能已禁用，使用单帧检测")
            
            # 3. 初始化坐标转换器（传入camera_controller避免重复创建）
            self.coordinate_transformer = CoordinateTransformer(self.config)
            # 设置坐标转换器使用同一个camera_controller实例
            if self.camera_controller:
                self.coordinate_transformer.camera_controller = self.camera_controller
            
            # 4. 连接相机
            if not self.coordinate_transformer.connect_camera():
                raise RuntimeError("相机连接失败")
            
            # 5. 初始化物体分析器
            self.object_analyzer = ObjectAnalyzer(self.coordinate_transformer, self.config)
            
            # 6. 初始化点云可视化器
            self.pointcloud_visualizer = PointCloudVisualizer(self.coordinate_transformer)
            
            # 7. 初始化图像处理器
            self.image_processor = ImageProcessor(self.config)
            
            # 8. 初始化图像预处理器
            self.image_preprocessor = ImagePreprocessor(self.config)
            
            # 9. 初始化输出生成器
            self.output_generator = ObjectOutputGenerator()
            
            # 10. 初始化文字识别组件（如果启用）
            if self.config.text_recognition.enabled:
                print("📝 初始化文字识别组件...")
                try:
                    # 初始化仿射变换映射器
                    calibration_path = self.config.text_recognition.get_calibration_file_path()
                    self.affine_mapper = AffineMapper(calibration_path)
                    
                    # 初始化文字识别器
                    self.text_recognizer = TextRecognizer(
                        rec_model_name=self.config.text_recognition.recognition_model,
                        det_model_name=self.config.text_recognition.detection_model
                    )
                    
                    print("✅ 文字识别组件初始化完成")
                except Exception as e:
                    print(f"⚠️ 文字识别组件初始化失败: {e}")
                    print("   将在不启用文字识别的情况下继续运行")
                    self.config.text_recognition.enabled = False
            else:
                print("⚠️ 文字识别功能已禁用")
            
            self.is_initialized = True
            print("✅ 系统初始化完成！")
            
        except Exception as e:
            print(f"❌ 系统初始化失败: {e}")
            self.is_initialized = False
            raise

    def _display_height_statistics(self, analysis_results):
        """
        显示所有物体的高度统计信息

        Args:
            analysis_results: 物体分析结果列表
        """
        if not analysis_results:
            return

        print("\n" + "="*40)
        print("📏 桌面坐标系高度统计摘要")
        print("="*40)

        # 收集所有物体的高度信息
        real_objects = []
        flat_objects = []

        for result in analysis_results:
            if hasattr(result, 'average_height') and hasattr(result, 'is_real'):
                height_info = {
                    'name': result.class_name if hasattr(result, 'class_name') else '未知',
                    'average_height': result.average_height,
                    'height_range': result.height_range if hasattr(result, 'height_range') else (0.0, 0.0),
                    'point_count': result.point_count if hasattr(result, 'point_count') else 0
                }

                if result.is_real:
                    real_objects.append(height_info)
                else:
                    flat_objects.append(height_info)

        # 显示真实物体的高度统计
        if real_objects:
            print(f"\n🔺 真实物体高度统计 ({len(real_objects)} 个):")
            real_heights = [obj['average_height'] for obj in real_objects]
            print(f"   整体平均高度: {np.mean(real_heights):.1f} mm")
            print(f"   高度范围: {np.min(real_heights):.1f} ~ {np.max(real_heights):.1f} mm")
            print(f"   高度标准差: {np.std(real_heights):.1f} mm")

            print(f"\n   详细信息:")
            for i, obj in enumerate(real_objects, 1):
                height_diff = obj['height_range'][1] - obj['height_range'][0] if isinstance(obj['height_range'], tuple) else 0.0
                print(f"     {i}. {obj['name']}: 平均{obj['average_height']:.1f}mm, 高度差{height_diff:.1f}mm ({obj['point_count']}点)")

        # 显示平面物体的高度统计
        if flat_objects:
            print(f"\n⚪ 平面物体高度统计 ({len(flat_objects)} 个):")
            flat_heights = [obj['average_height'] for obj in flat_objects]
            print(f"   整体平均高度: {np.mean(flat_heights):.1f} mm")
            print(f"   高度范围: {np.min(flat_heights):.1f} ~ {np.max(flat_heights):.1f} mm")
            print(f"   高度标准差: {np.std(flat_heights):.1f} mm")

            print(f"\n   详细信息:")
            for i, obj in enumerate(flat_objects, 1):
                height_diff = obj['height_range'][1] - obj['height_range'][0] if isinstance(obj['height_range'], tuple) else 0.0
                print(f"     {i}. {obj['name']}: 平均{obj['average_height']:.1f}mm, 高度差{height_diff:.1f}mm ({obj['point_count']}点)")

        # 显示整体统计
        if real_objects or flat_objects:
            all_heights = [obj['average_height'] for obj in real_objects + flat_objects]
            print(f"\n📊 整体高度统计:")
            print(f"   所有物体数量: {len(all_heights)} 个")
            print(f"   整体平均高度: {np.mean(all_heights):.1f} mm")
            print(f"   整体高度范围: {np.min(all_heights):.1f} ~ {np.max(all_heights):.1f} mm")
            print(f"   真实物体比例: {len(real_objects)}/{len(all_heights)} ({len(real_objects)/len(all_heights)*100:.1f}%)")

        print("="*40)
        print()
    
    def _initialize_text_recognition_if_needed(self):
        """
        按需初始化文字识别组件
        只有在真正需要文字识别时才加载模型，避免不必要的资源消耗
        """
        if not self.config.text_recognition.enabled:
            return False
        
        # 如果已经初始化，直接返回
        if self.text_recognizer is not None and self.affine_mapper is not None:
            return True
        
        try:
            print("📝 按需初始化文字识别组件...")
            
            # 初始化仿射变换映射器
            calibration_path = self.config.text_recognition.get_calibration_file_path()
            self.affine_mapper = AffineMapper(calibration_path)
            
            # 初始化文字识别器
            self.text_recognizer = TextRecognizer(
                rec_model_name=self.config.text_recognition.recognition_model,
                det_model_name=self.config.text_recognition.detection_model
            )
            
            print("✅ 文字识别组件按需初始化完成")
            return True
            
        except Exception as e:
            print(f"❌ 文字识别组件按需初始化失败: {e}")
            print("   将在不启用文字识别的情况下继续运行")
            self.config.text_recognition.enabled = False
            return False

    def cleanup(self):
        """清理资源"""
        if not hasattr(self, '_cleanup_done'):
            self._cleanup_done = False
        
        if self._cleanup_done:
            return
            
        try:
            print("🧹 清理系统资源...")
            
            # 清理文字识别器
            if self.text_recognizer:
                self.text_recognizer.cleanup()
                self.text_recognizer = None

            # 清理多帧检测器
            if self.multi_frame_detector:
                # 保存最终的跟踪结果
                try:
                    self.multi_frame_detector.save_tracking_results()
                except Exception as e:
                    print(f"⚠️ 保存跟踪结果失败: {e}")
                self.multi_frame_detector = None
            
            # 清理坐标转换器（包含相机资源）
            if self.coordinate_transformer:
                self.coordinate_transformer.cleanup()
                self.coordinate_transformer = None
            
            # 清理相机控制器（如果单独存在）
            if self.camera_controller:
                self.camera_controller.cleanup()
                self.camera_controller = None
            
            # 清理窗口
            cv2.destroyAllWindows()
            
            self._cleanup_done = True
            print("✅ 资源清理完成")
            
        except Exception as e:
            print(f"❌ 资源清理失败: {e}")
            self._cleanup_done = True

    def _filter_detections_for_text_recognition(self, detections: List[Detection],
                                              analysis_results: List[ObjectAnalysisResult]) -> List[Detection]:
        """
        过滤需要进行文字识别的检测结果
        只对真实物体且不在预定义14类中的物品进行文字识别

        Args:
            detections: 所有检测结果
            analysis_results: 物体分析结果

        Returns:
            需要进行文字识别的检测结果列表
        """
        # 预定义的8类物体（不需要文字识别，会输出到最终txt文件）
        predefined_classes = {
            'hanger', 'toothbrush', 'jelly', 'box',
            'can', 'bottle', 'banana', 'orange'
        }

        # 创建分析结果的索引映射
        analysis_map = {}
        for result in analysis_results:
            if hasattr(result, 'detection_index'):
                analysis_map[result.detection_index] = result

        filtered_detections = []

        for i, detection in enumerate(detections):
            # 检查是否有对应的分析结果
            analysis_result = analysis_map.get(i)

            if analysis_result is None:
                print(f"   ⚠️ 检测物体 {detection.class_name} 没有分析结果，跳过文字识别")
                continue

            # 检查是否为真实物体
            if not (hasattr(analysis_result, 'is_real_object') and analysis_result.is_real_object):
                print(f"   ⚪️ 物体 {detection.class_name} 不是真实物体，跳过文字识别")
                continue

            # 检查是否在预定义类别中
            class_name_lower = detection.class_name.lower()
            if class_name_lower in predefined_classes:
                print(f"   📋 物体 {detection.class_name} 在预定义类别中，跳过文字识别")
                continue

            # 符合条件：真实物体且不在预定义类别中
            print(f"   ✅ 物体 {detection.class_name} 需要文字识别")
            filtered_detections.append(detection)

        return filtered_detections

    def _map_detections_to_low_res(self, detections: List[Detection], 
                                  high_res_shape: Tuple[int, int], 
                                  low_res_shape: Tuple[int, int]) -> List[Detection]:
        """
        将高分辨率检测结果映射回640×480分辨率
        
        Args:
            detections: 高分辨率检测结果
            high_res_shape: 高分辨率图像形状 (height, width)
            low_res_shape: 低分辨率图像形状 (height, width)
            
        Returns:
            映射后的检测结果列表
        """
        if not self.affine_mapper or not self.affine_mapper.is_calibration_loaded():
            print("⚠️ 仿射变换未初始化，使用简单缩放映射")
            return self._simple_scale_detections(detections, high_res_shape, low_res_shape)
        
        mapped_detections = []
        
        for i, detection in enumerate(detections):
            try:
                # 获取高分辨率边界框
                x1, y1, x2, y2 = detection.bbox

                # 检查是否有仿射变换矩阵
                if hasattr(self.affine_mapper, 'affine_matrix_1920_to_640') and self.affine_mapper.affine_matrix_1920_to_640 is not None:
                    # 使用新的逆变换函数映射边界框
                    new_x1, new_y1, new_x2, new_y2 = self.affine_mapper.map_bbox_1920_to_640((x1, y1, x2, y2))

                    # 处理掩膜映射
                    mapped_mask = None
                    if detection.mask is not None and self.affine_mapper:
                        try:
                            # 使用新的逆变换函数映射掩膜
                            mapped_mask = self.affine_mapper.map_mask_1920_to_640(detection.mask)
                        except Exception as e:
                            print(f"⚠️ 掩膜映射失败: {e}")

                    # 创建新的检测结果
                    mapped_detection = Detection(
                        box=np.array([new_x1, new_y1, new_x2, new_y2]),
                        confidence=detection.confidence,
                        class_id=detection.class_id,
                        class_name=detection.class_name,
                        mask=mapped_mask
                    )

                    mapped_detections.append(mapped_detection)

                    print(f"   检测{i+1}: {detection.class_name} "
                          f"({x1},{y1},{x2},{y2}) → ({new_x1},{new_y1},{new_x2},{new_y2})")

                else:
                    print(f"⚠️ 缺少1920→640的仿射变换矩阵")
                    # 作为备选，使用简单的缩放
                    scale_x = low_res_shape[1] / high_res_shape[1]  # width scale
                    scale_y = low_res_shape[0] / high_res_shape[0]  # height scale
                    
                    new_x1 = int(x1 * scale_x)
                    new_y1 = int(y1 * scale_y)
                    new_x2 = int(x2 * scale_x)
                    new_y2 = int(y2 * scale_y)
                    
                    # 使用简单缩放时也处理掩膜
                    mapped_mask = None
                    if detection.mask is not None:
                        try:
                            # 简单缩放掩膜
                            mapped_mask = cv2.resize(detection.mask.astype(np.uint8), 
                                                   (low_res_shape[1], low_res_shape[0]), 
                                                   interpolation=cv2.INTER_NEAREST)
                            mapped_mask = mapped_mask.astype(bool)
                        except Exception as e:
                            print(f"⚠️ 掩膜缩放失败: {e}")
                    
                    mapped_detection = Detection(
                        box=np.array([new_x1, new_y1, new_x2, new_y2]),
                        confidence=detection.confidence,
                        class_id=detection.class_id,
                        class_name=detection.class_name,
                        mask=mapped_mask
                    )
                    
                    mapped_detections.append(mapped_detection)
                    
            except Exception as e:
                print(f"⚠️ 映射检测结果{i+1}失败: {e}")
                continue
        
        return mapped_detections

    def _simple_scale_detections(self, detections: List[Detection],
                                high_res_shape: Tuple[int, int],
                                low_res_shape: Tuple[int, int]) -> List[Detection]:
        """
        使用改进的缩放将高分辨率检测结果映射到低分辨率

        Args:
            detections: 高分辨率检测结果
            high_res_shape: 高分辨率图像形状 (height, width)
            low_res_shape: 低分辨率图像形状 (height, width)

        Returns:
            映射后的检测结果列表
        """
        print(f"🔄 使用改进缩放映射: {high_res_shape} → {low_res_shape}")

        # 计算精确的缩放比例
        scale_x = low_res_shape[1] / high_res_shape[1]  # width scale
        scale_y = low_res_shape[0] / high_res_shape[0]  # height scale

        print(f"   缩放比例: x={scale_x:.3f}, y={scale_y:.3f}")

        mapped_detections = []

        for i, detection in enumerate(detections):
            try:
                # 获取高分辨率边界框
                x1, y1, x2, y2 = detection.bbox

                # 应用缩放（使用更精确的计算）
                new_x1 = round(x1 * scale_x)
                new_y1 = round(y1 * scale_y)
                new_x2 = round(x2 * scale_x)
                new_y2 = round(y2 * scale_y)

                # 确保坐标在有效范围内
                new_x1 = max(0, min(new_x1, low_res_shape[1] - 1))
                new_y1 = max(0, min(new_y1, low_res_shape[0] - 1))
                new_x2 = max(0, min(new_x2, low_res_shape[1] - 1))
                new_y2 = max(0, min(new_y2, low_res_shape[0] - 1))

                # 处理掩膜（改进版本）
                mapped_mask = None
                if detection.mask is not None:
                    try:
                        # 获取原始掩膜尺寸
                        mask_height, mask_width = detection.mask.shape
                        print(f"   原始掩膜尺寸: {mask_width}x{mask_height}")
                        
                        # 确保掩膜尺寸与高分辨率图像匹配
                        if mask_height != high_res_shape[0] or mask_width != high_res_shape[1]:
                            print(f"   ⚠️ 掩膜尺寸不匹配，调整掩膜尺寸")
                            # 调整掩膜尺寸以匹配高分辨率图像
                            detection.mask = cv2.resize(detection.mask.astype(np.uint8),
                                                       (high_res_shape[1], high_res_shape[0]),
                                                       interpolation=cv2.INTER_NEAREST)
                            detection.mask = detection.mask.astype(bool)
                        
                        # 使用更精确的掩膜缩放
                        mapped_mask = cv2.resize(detection.mask.astype(np.uint8),
                                               (low_res_shape[1], low_res_shape[0]),
                                               interpolation=cv2.INTER_NEAREST)
                        mapped_mask = mapped_mask.astype(bool)
                        
                        # 验证掩膜质量
                        mask_area_high = np.sum(detection.mask)
                        mask_area_low = np.sum(mapped_mask)
                        area_ratio = mask_area_low / (mask_area_high * scale_x * scale_y) if mask_area_high > 0 else 0
                        
                        print(f"   掩膜面积: 高分辨率={mask_area_high}, 低分辨率={mask_area_low}, 比例={area_ratio:.3f}")
                        
                        if area_ratio < 0.8 or area_ratio > 1.2:
                            print(f"   ⚠️ 掩膜面积比例异常，可能存在问题")
                        
                    except Exception as e:
                        print(f"⚠️ 掩膜缩放失败: {e}")
                        import traceback
                        traceback.print_exc()

                # 创建新的检测结果
                mapped_detection = Detection(
                    box=np.array([new_x1, new_y1, new_x2, new_y2]),
                    confidence=detection.confidence,
                    class_id=detection.class_id,
                    class_name=detection.class_name,
                    mask=mapped_mask
                )

                mapped_detections.append(mapped_detection)

                print(f"   检测{i+1}: {detection.class_name} "
                      f"({x1},{y1},{x2},{y2}) → ({new_x1},{new_y1},{new_x2},{new_y2})")

            except Exception as e:
                print(f"⚠️ 改进缩放映射失败: {e}")
                import traceback
                traceback.print_exc()
                continue

        print(f"✅ 改进缩放映射完成: {len(detections)} → {len(mapped_detections)}")
        return mapped_detections

    def _map_mask_to_low_res(self, mask: np.ndarray,
                            high_res_shape: Tuple[int, int], 
                            low_res_shape: Tuple[int, int]) -> Optional[np.ndarray]:
        """
        将高分辨率掩膜映射回640×480分辨率
        
        Args:
            mask: 高分辨率掩膜
            high_res_shape: 高分辨率图像形状 (height, width)
            low_res_shape: 低分辨率图像形状 (height, width)
            
        Returns:
            映射后的掩膜
        """
        try:
            if mask is None:
                return None
            
            # 确保掩膜是2D的
            if len(mask.shape) > 2:
                mask = mask.squeeze()
            
            # 使用仿射变换映射掩膜
            if hasattr(self.affine_mapper, 'affine_matrix_1920_to_640'):
                # 使用仿射变换
                mapped_mask = cv2.warpAffine(
                    mask.astype(np.uint8),
                    self.affine_mapper.affine_matrix_1920_to_640,
                    (low_res_shape[1], low_res_shape[0]),  # (width, height)
                    flags=cv2.INTER_NEAREST
                )
                return mapped_mask.astype(bool)
            else:
                # 备选：简单缩放
                mapped_mask = cv2.resize(
                    mask.astype(np.uint8),
                    (low_res_shape[1], low_res_shape[0]),  # (width, height)
                    interpolation=cv2.INTER_NEAREST
                )
                return mapped_mask.astype(bool)
                
        except Exception as e:
            print(f"❌ 掩膜映射失败: {e}")
            return None

    def interactive_frame_capture(self):
        """
        交互式帧获取模式
        在屏幕上显示相机实时画面，按空格键截取当前帧进行处理
        """
        if not self.is_initialized:
            print("❌ System not initialized")
            return
        
        ResultDisplay.display_interactive_mode_instructions()
        # 增加更清晰的提示
        print("\n💡 操作提示：请先将相机对准目标桌面，确保画面清晰稳定后，按【空格键】进行检测。")
        
        # 创建窗口
        cv2.namedWindow('Camera Preview', cv2.WINDOW_NORMAL)
        cv2.resizeWindow('Camera Preview', 1200, 900)
        
        try:
            while True:
                # 获取当前帧，使用较短的超时以减少队列积压
                frames = self.coordinate_transformer.pipeline.wait_for_frames(50)
                if frames is None:
                    continue
                
                # 检查帧状态
                depth_frame = frames.get_depth_frame()
                color_frame = frames.get_color_frame()
                
                if depth_frame is None or color_frame is None:
                    continue
                
                # 处理彩色图像用于显示
                color_image = self.image_processor.process_color_frame(color_frame)
                if color_image is None:
                    continue
                
                # 在图像上添加UI覆盖层
                display_image = color_image.copy()
                self.image_processor.add_ui_overlay(display_image)
                
                # 显示图像
                cv2.imshow('Camera Preview', display_image)
                
                # 等待按键
                key = cv2.waitKey(1) & 0xFF
                
                if key == ord('q') or key == 27:  # Q键或ESC键
                    print("🛑 User exit")
                    break
                elif key == ord(' '):  # 空格键
                    print("📸 Capturing current frame...")
                    should_continue = self._process_captured_frame(frames, color_image)
                    if should_continue is False:
                        print("🛑 退出程序")
                        break
                elif key == ord('s'):  # S键
                    print("💾 Saving current frame...")
                    self.image_processor.save_current_frame(color_image)
                
        except KeyboardInterrupt:
            print("🛑 User interrupt")
        except Exception as e:
            print(f"❌ Interactive mode failed: {e}")
        finally:
            cv2.destroyAllWindows()
    
    def _process_captured_frame(self, frames: FrameSet, color_image):
        """
        处理截取的帧

        Args:
            frames: 相机帧数据
            color_image: 彩色图像

        Returns:
            bool: True表示继续运行，False表示退出程序
        """
        result_window_created = False
        high_res_image_path = None
        
        try:
            image_shape = color_image.shape[:2]  # (height, width)

            # 1. 获取高分辨率图像（用于YOLO检测和文字识别）
            high_res_image = None
            print("\n📸 获取高分辨率图像用于YOLO检测...")
            if self.camera_controller:
                high_res_image = self.camera_controller.get_high_resolution_image()

                if high_res_image is not None:
                    print(f"✅ 成功获取高分辨率图像: {high_res_image.shape}")
                else:
                    print("⚠️ 高分辨率图像获取失败，将使用低分辨率图像")
            else:
                print("⚠️ 相机控制器未初始化，将使用低分辨率图像")

            # 2. 测试图像对齐（640×480）
            print("\n🔍 测试深度图像与彩色图像对齐（640×480）...")
            aligned_result_640 = self.coordinate_transformer.align_depth_to_color(frames, "640x480")
            if aligned_result_640:
                aligned_depth_640, aligned_color_640 = aligned_result_640
                print(f"✅ 640×480图像对齐成功: 深度{aligned_depth_640.shape} 彩色{aligned_color_640.shape}")
            else:
                print("⚠️ 640×480图像对齐失败")

            # 3. 如果有高分辨率图像，获取1920×1080的对齐深度图像
            aligned_result_1920 = None
            if high_res_image is not None:
                print("\n🔍 获取1920×1080的对齐深度图像...")
                aligned_result_1920 = self.coordinate_transformer.align_depth_to_color(frames, "1920x1080")
                if aligned_result_1920:
                    aligned_depth_1920, aligned_color_1920 = aligned_result_1920
                    print(f"✅ 1920×1080图像对齐成功: 深度{aligned_depth_1920.shape} 彩色{aligned_color_1920.shape}")
                else:
                    print("⚠️ 1920×1080图像对齐失败，将使用映射方案")

            # 3. 桌面平面检测（详细输出在coordinate_transformer中）
            if not self.coordinate_transformer.auto_detect_table_plane(frames=frames):
                print("⚠️ 未能成功检测到桌面，将使用默认坐标系。3D可视化可能不准确。")
            else:
                print("✅ 桌面平面检测完成，将分析所有检测到的物体")
            
            # 4. YOLO物体检测（优先使用高分辨率图像）
            detection_image = high_res_image if high_res_image is not None else color_image
            detection_image_shape = detection_image.shape[:2]  # (height, width)

            print(f"\n🖼️  使用图像进行YOLO检测: {detection_image.shape[1]}x{detection_image.shape[0]}")

            if self.use_multi_frame_detection and self.multi_frame_detector:
                print("🎯 运行多帧YOLO检测...")
                # 使用多帧检测器
                current_detections, stable_detections = self.multi_frame_detector.detect_objects_multi_frame(detection_image)

                # 将高分辨率检测结果映射回640×480以配合深度数据
                if high_res_image is not None:
                    print("� 将稳定检测结果映射回640×480以配合深度数据...")
                    mapped_stable_detections = self._map_detections_to_low_res(stable_detections, detection_image_shape, image_shape)
                    detections = mapped_stable_detections
                    print(f"✅ 成功映射 {len(detections)} 个稳定检测结果到640×480分辨率")
                else:
                    detections = stable_detections

                # 更新统计信息
                self.detection_count += len(current_detections)

                # 显示多帧检测统计
                stats = self.multi_frame_detector.get_tracking_stats()
                print(f"📊 多帧检测统计:")
                print(f"   当前帧检测: {len(current_detections)} 个")
                print(f"   稳定物体: {len(stable_detections)} 个")
                print(f"   跟踪对象总数: {stats['total_tracked_objects']}")
                print(f"   稳定对象总数: {stats['stable_objects']}")

            else:
                print("🔍 运行单帧YOLO检测...")
                detections = self.yolo_detector.detect_objects(detection_image)
                self.detection_count += len(detections)

                # 将高分辨率检测结果映射回640×480以配合深度数据
                if high_res_image is not None:
                    print("🔄 将高分辨率检测结果映射回640×480以配合深度数据...")
                    mapped_detections = self._map_detections_to_low_res(detections, detection_image_shape, image_shape)
                    detections = mapped_detections
                    print(f"✅ 成功映射 {len(detections)} 个检测结果到640×480分辨率")

            # 调试坐标映射问题（如果有高分辨率图像）
            if high_res_image is not None and detections:
                self.debug_coordinate_mapping(detections, frames)
            
            if not detections:
                print("⚠️ No objects detected")
                self.frame_count += 1
                return True  # 继续运行
             
            print(f"Detected {len(detections)} objects")
            
            # 5. 物体分析（选择最佳分析策略）
            print("\n🧠 Analyzing objects...")

            # 使用640×480帧进行物体分析
            print("🔄 使用640×480帧进行物体分析")
            analysis_results = self.object_analyzer.analyze_objects_batch(
                frames, detections, image_shape, "mask"
            )

            self.analysis_count += len(analysis_results)
            
            # 6. 文字识别（如果启用且有高分辨率图像）
            text_results = []
            if (self.config.text_recognition.enabled and self.affine_mapper and
                self.text_recognizer and high_res_image is not None):
                try:
                    print("\n📝 开始文字识别流程...")

                    # 5.1 过滤需要进行文字识别的检测结果
                    # 只对真实物体且不在预定义8类中的物品进行文字识别
                    filtered_detections = self._filter_detections_for_text_recognition(detections, analysis_results)

                    if not filtered_detections:
                        print("⚠️ 没有需要进行文字识别的物体")
                    else:
                        print(f"📝 找到 {len(filtered_detections)} 个需要文字识别的物体")

                        # 5.2 映射过滤后的检测结果到高分辨率
                        # 注意：filtered_detections现在是640×480分辨率的，需要映射到1920×1080
                        print("🔄 映射检测结果到高分辨率...")
                        mapped_regions = self.affine_mapper.map_detections_batch(filtered_detections, image_shape)

                        if mapped_regions:
                            # 5.3 在映射区域中识别文字
                            text_results = self.text_recognizer.recognize_text_in_mapped_regions(
                                high_res_image, mapped_regions, self.affine_mapper
                            )

                            # 5.4 保存文字识别结果（仅文本文件）
                            if text_results and self.config.text_recognition.save_text_results:
                                timestamp = time.strftime("%Y%m%d_%H%M%S")
                                text_file = f"output/text_recognition_frame_{self.frame_count:04d}_{timestamp}.txt"
                                self.text_recognizer.save_text_results(text_results, text_file)
                        else:
                            print("⚠️ 没有成功映射的区域")

                except Exception as e:
                    print(f"❌ 文字识别过程失败: {e}")
                    import traceback
                    traceback.print_exc()
            elif self.config.text_recognition.enabled and high_res_image is None:
                print("⚠️ 文字识别已启用但无高分辨率图像，跳过文字识别")
            
            # 7. 显示结果
            ResultDisplay.display_results(detections, analysis_results)

            # 7.1 显示高度统计信息
            self._display_height_statistics(analysis_results)
            
            # 7.2 显示文字识别结果
            if text_results:
                print(f"\n📝 文字识别结果摘要:")
                print(f"   识别到 {len(text_results)} 个文字")
                for i, result in enumerate(text_results, 1):
                    print(f"   {i}. '{result.text}' (置信度: {result.confidence:.3f}, 来自: {result.region_name})")

            # 8. 可视化检测结果（使用原分辨率图像）
            annotated_image = color_image.copy()
            
            # 绘制分割掩码
            self.image_processor.draw_masks(annotated_image, detections)

            # 绘制YOLO检测框和分析结果
            self.image_processor.annotate_detections(annotated_image, detections)
            
            # 使用分析结果进行标注
            self.image_processor.annotate_analysis_results(annotated_image, analysis_results)
            
            # 9. 显示结果图像
            try:
                cv2.namedWindow('Analysis Result', cv2.WINDOW_NORMAL)
                cv2.resizeWindow('Analysis Result', 800, 600)
                cv2.imshow('Analysis Result', annotated_image)
                result_window_created = True
                print("✅ 结果窗口已创建并显示")
            except Exception as e:
                print(f"⚠️ 创建结果窗口失败: {e}")
                result_window_created = False
            
            # 10. 保存结果
            self.image_processor.save_frame_results(
                f"frame_{self.frame_count:04d}", annotated_image, analysis_results, detections
            )
            
            # 11. 生成物体检测输出文件
            frame_name = f"frame_{self.frame_count:04d}"
            self.output_generator.save_output_file(analysis_results, frame_name)
            self.output_generator.print_detection_summary(analysis_results)
            
            self.frame_count += 1
            
            ResultDisplay.display_processing_complete()

            # 非阻塞的用户交互
            print("\n💡 结果显示中...")
            print("   - 按任意键关闭结果窗口并返回相机预览")
            print("   - 按 'p' 或 '3' 显示3D点云可视化")
            print("   - 按 't' 显示桌面检测可视化")
            if self.use_multi_frame_detection and self.multi_frame_detector:
                print("   - 按 'm' 显示多帧跟踪状态")
                print("   - 按 'r' 重置多帧跟踪")
            print("   - 按 'q' 退出程序")

            # 等待用户操作
            while True:
                key = cv2.waitKey(100) & 0xFF  # 100ms超时

                if key == ord('p') or key == ord('P') or key == ord('3'):
                    print("🔍 显示交互式3D桌面坐标系点云...")
                    try:
                        self.pointcloud_visualizer.display_interactive_pointcloud_with_bboxes(analysis_results, frames, color_image)
                    except Exception as e:
                        print(f"⚠️ 3D可视化失败: {e}")
                    break
                elif key == ord('t') or key == ord('T'):
                    print("🔍 显示桌面检测可视化...")
                    try:
                        self.pointcloud_visualizer.visualize_table_detection(frames)
                    except Exception as e:
                        print(f"⚠️ 桌面检测可视化失败: {e}")
                    break
                elif key == ord('m') or key == ord('M'):
                    if self.use_multi_frame_detection and self.multi_frame_detector:
                        print("🎯 显示多帧跟踪状态...")
                        try:
                            # 显示跟踪统计
                            stats = self.multi_frame_detector.get_tracking_stats()
                            print(f"📊 多帧跟踪统计:")
                            print(f"   处理帧数: {stats['frame_count']}")
                            print(f"   跟踪对象总数: {stats['total_tracked_objects']}")
                            print(f"   稳定对象总数: {stats['stable_objects']}")
                            print(f"   类别统计: {stats['class_counts']}")
                            print(f"   稳定类别统计: {stats['stable_class_counts']}")
                            print(f"   融合方法: {stats['fusion_method']}")
                            print(f"   融合帧数: {stats['n_frames']}")
                            print(f"   稳定阈值: {stats['stable_threshold']}")

                            # 可视化跟踪状态
                            if detection_image is not None:
                                tracking_vis = self.multi_frame_detector.visualize_tracking_status(detection_image)
                                if tracking_vis is not None:
                                    cv2.namedWindow('Multi-Frame Tracking Status', cv2.WINDOW_NORMAL)
                                    cv2.resizeWindow('Multi-Frame Tracking Status', 800, 600)
                                    cv2.imshow('Multi-Frame Tracking Status', tracking_vis)
                                    print("   按任意键关闭跟踪状态窗口...")
                                    cv2.waitKey(0)
                                    cv2.destroyWindow('Multi-Frame Tracking Status')
                        except Exception as e:
                            print(f"⚠️ 多帧跟踪状态显示失败: {e}")
                    else:
                        print("⚠️ 多帧检测功能未启用")
                    break
                elif key == ord('r') or key == ord('R'):
                    if self.use_multi_frame_detection and self.multi_frame_detector:
                        print("🔄 重置多帧跟踪状态...")
                        self.multi_frame_detector.reset_tracking()
                        print("✅ 多帧跟踪状态已重置")
                    else:
                        print("⚠️ 多帧检测功能未启用")
                    break
                elif key == ord('q') or key == 27:  # Q键或ESC键
                    print("🛑 用户选择退出")
                    return False  # 返回False表示退出主循环
                elif key != 255:  # 任何其他按键（255表示超时无按键）
                    print("📸 返回相机预览模式")
                    break

                # 检查窗口是否被关闭
                try:
                    if cv2.getWindowProperty('Analysis Result', cv2.WND_PROP_VISIBLE) < 1:
                        print("📸 结果窗口已关闭，返回相机预览模式")
                        break
                except:
                    # 窗口不存在或已关闭
                    print("📸 结果窗口已关闭，返回相机预览模式")
                    break
            
        except Exception as e:
            print(f"❌ Frame processing failed: {e}")
            import traceback
            traceback.print_exc()
            return True  # 即使出错也继续运行
        finally:
            # 确保窗口正确关闭
            if result_window_created:
                try:
                    cv2.destroyWindow('Analysis Result')
                except:
                    pass
            # 保留主窗口，继续运行

        return True  # 默认继续运行

    def debug_coordinate_mapping(self, detections: List[Detection], frames):
        """
        调试坐标映射问题
        """
        print("\n🔍 调试坐标映射问题...")

        if not detections:
            print("❌ 没有检测结果可调试")
            return

        # 获取深度帧信息
        depth_frame = frames.get_depth_frame()
        if depth_frame:
            depth_width = depth_frame.get_width()
            depth_height = depth_frame.get_height()
            print(f"📏 深度帧尺寸: {depth_width}×{depth_height}")

            # 获取深度数据
            depth_data = np.frombuffer(depth_frame.get_data(), dtype=np.uint16)
            depth_image = depth_data.reshape((depth_height, depth_width))

            # 统计深度图像
            valid_depths = depth_image[depth_image > 0]
            print(f"📊 深度图像统计:")
            print(f"   总像素: {depth_image.size:,}")
            print(f"   有效深度像素: {len(valid_depths):,} ({len(valid_depths)/depth_image.size*100:.1f}%)")
            if len(valid_depths) > 0:
                print(f"   深度范围: {np.min(valid_depths)} ~ {np.max(valid_depths)}")

        # 检查第一个检测结果的坐标映射
        detection = detections[0]
        print(f"\n🎯 调试第一个检测结果: {detection.class_name}")
        print(f"   原始边界框: {detection.bbox}")

        # 检查映射后的坐标是否有有效深度
        x1, y1, x2, y2 = detection.bbox
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2

        print(f"   中心坐标: ({center_x}, {center_y})")

        if depth_frame and 0 <= center_x < depth_width and 0 <= center_y < depth_height:
            center_depth = depth_image[center_y, center_x]
            print(f"   中心深度值: {center_depth}")

            # 检查边界框区域的深度值
            x1_safe = max(0, min(x1, depth_width-1))
            y1_safe = max(0, min(y1, depth_height-1))
            x2_safe = max(0, min(x2, depth_width-1))
            y2_safe = max(0, min(y2, depth_height-1))

            if x2_safe > x1_safe and y2_safe > y1_safe:
                bbox_depths = depth_image[y1_safe:y2_safe, x1_safe:x2_safe]
                valid_bbox_depths = bbox_depths[bbox_depths > 0]
                print(f"   边界框区域深度统计:")
                print(f"     区域尺寸: {x2_safe-x1_safe}×{y2_safe-y1_safe}")
                print(f"     有效深度像素: {len(valid_bbox_depths)}/{bbox_depths.size}")
                if len(valid_bbox_depths) > 0:
                    print(f"     深度范围: {np.min(valid_bbox_depths)} ~ {np.max(valid_bbox_depths)}")
                else:
                    print("     ❌ 边界框区域没有有效深度值！")


def main():
    """主函数"""
    system = None
    try:
        system = YOLOPointCloudSystem()
        system.initialize()
        system.interactive_frame_capture()
    except Exception as e:
        print(f"❌ 系统运行时发生严重错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if system:
            system.cleanup()

if __name__ == '__main__':
    main() 