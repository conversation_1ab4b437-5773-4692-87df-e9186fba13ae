#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多帧YOLO检测器模块
实现连续多帧YOLO检测、掩膜时序融合、稳定ID跟踪和数量输出功能
核心目标：减少掩膜抖动、漏检，同时不显著降低推理速度
"""

import cv2
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
import time
from collections import deque
from dataclasses import dataclass, field
import uuid

from yolo_detector import YOLODetector, Detection
from config import SystemConfig


@dataclass
class TrackedObject:
    """跟踪的物体对象"""
    object_id: str
    class_name: str
    class_id: int
    detections_history: deque = field(default_factory=lambda: deque(maxlen=10))  # 最近10帧的检测历史
    masks_history: deque = field(default_factory=lambda: deque(maxlen=10))  # 最近10帧的掩膜历史
    confidences_history: deque = field(default_factory=lambda: deque(maxlen=10))  # 最近10帧的置信度历史
    last_seen_frame: int = 0
    consecutive_frames: int = 0  # 连续出现的帧数
    is_stable: bool = False  # 是否为稳定物体（连续出现≥K帧）
    
    def add_detection(self, detection: Detection, frame_idx: int, mask: Optional[np.ndarray] = None):
        """添加新的检测结果"""
        self.detections_history.append(detection)
        self.masks_history.append(mask if mask is not None else detection.mask)
        self.confidences_history.append(detection.confidence)
        self.last_seen_frame = frame_idx
        self.consecutive_frames += 1

    def check_iou_stability(self, new_detection: Detection, iou_threshold: float = 0.3) -> bool:
        """
        检查新检测与历史检测的IoU稳定性

        Args:
            new_detection: 新的检测结果
            iou_threshold: IoU稳定性阈值

        Returns:
            True表示稳定，False表示不稳定需要重置
        """
        if not self.detections_history:
            return True  # 第一次检测，认为稳定

        # 与最近的检测计算IoU
        latest_detection = self.detections_history[-1]
        bbox_iou = self._calculate_bbox_iou(new_detection.bbox, latest_detection.bbox)

        # 如果有掩膜，也计算掩膜IoU
        mask_iou = 1.0  # 默认认为稳定
        if (new_detection.mask is not None and
            len(self.masks_history) > 0 and
            self.masks_history[-1] is not None):
            try:
                mask_iou = self._calculate_mask_iou(new_detection.mask, self.masks_history[-1])
            except:
                mask_iou = bbox_iou  # 掩膜计算失败时使用边界框IoU

        # 综合IoU（边界框权重0.6，掩膜权重0.4）
        combined_iou = 0.6 * bbox_iou + 0.4 * mask_iou

        return combined_iou >= iou_threshold

    def _calculate_bbox_iou(self, bbox1: Tuple[int, int, int, int],
                           bbox2: Tuple[int, int, int, int]) -> float:
        """计算两个边界框的IoU"""
        x1_1, y1_1, x2_1, y2_1 = bbox1
        x1_2, y1_2, x2_2, y2_2 = bbox2

        # 计算交集
        x1_inter = max(x1_1, x1_2)
        y1_inter = max(y1_1, y1_2)
        x2_inter = min(x2_1, x2_2)
        y2_inter = min(y2_1, y2_2)

        if x2_inter <= x1_inter or y2_inter <= y1_inter:
            return 0.0

        inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)

        # 计算并集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union_area = area1 + area2 - inter_area

        if union_area <= 0:
            return 0.0

        return inter_area / union_area

    def _calculate_mask_iou(self, mask1: np.ndarray, mask2: np.ndarray) -> float:
        """计算两个掩膜的IoU"""
        if mask1.shape != mask2.shape:
            # 调整掩膜尺寸
            mask2 = cv2.resize(mask2.astype(np.uint8),
                             (mask1.shape[1], mask1.shape[0]),
                             interpolation=cv2.INTER_NEAREST).astype(bool)

        # 计算Jaccard相似度（IoU）
        intersection = np.logical_and(mask1, mask2).sum()
        union = np.logical_or(mask1, mask2).sum()

        if union == 0:
            return 1.0 if intersection == 0 else 0.0

        return intersection / union

    def get_average_confidence(self) -> float:
        """获取平均置信度"""
        if not self.confidences_history:
            return 0.0
        return np.mean(list(self.confidences_history))
    
    def get_fused_mask(self, fusion_method: str = "majority_vote", n_frames: int = 3) -> Optional[np.ndarray]:
        """获取融合后的掩膜"""
        if not self.masks_history:
            return None
        
        # 获取最近N帧的掩膜
        recent_masks = list(self.masks_history)[-n_frames:]
        recent_confidences = list(self.confidences_history)[-n_frames:]
        
        if not recent_masks or recent_masks[0] is None:
            return None
        
        # 确保所有掩膜尺寸一致
        mask_shape = recent_masks[0].shape
        valid_masks = []
        valid_confidences = []
        
        for i, mask in enumerate(recent_masks):
            if mask is not None and mask.shape == mask_shape:
                valid_masks.append(mask.astype(np.float32))
                valid_confidences.append(recent_confidences[i] if i < len(recent_confidences) else 0.5)
        
        if not valid_masks:
            return None
        
        if fusion_method == "majority_vote":
            return self._majority_vote_fusion(valid_masks)
        elif fusion_method == "confidence_weighted":
            return self._confidence_weighted_fusion(valid_masks, valid_confidences)
        else:
            return valid_masks[-1].astype(bool)  # 返回最新的掩膜
    
    def _majority_vote_fusion(self, masks: List[np.ndarray]) -> np.ndarray:
        """掩膜按位多数投票融合（改进版本，包含边缘平滑）"""
        if len(masks) == 1:
            return self._smooth_mask(masks[0].astype(bool))

        # 将掩膜转换为0/1
        binary_masks = [mask.astype(np.uint8) for mask in masks]

        # 按位求和
        sum_mask = np.sum(binary_masks, axis=0)

        # 多数投票：超过一半的帧中该像素为1，则融合结果为1
        threshold = len(masks) / 2
        fused_mask = sum_mask > threshold

        # 应用形态学处理和边缘平滑
        return self._smooth_mask(fused_mask.astype(bool))
    
    def _confidence_weighted_fusion(self, masks: List[np.ndarray], confidences: List[float]) -> np.ndarray:
        """置信度加权融合（改进版本，包含边缘平滑）"""
        if len(masks) == 1:
            return self._smooth_mask(masks[0].astype(bool))

        # 归一化置信度
        confidences = np.array(confidences)
        if np.sum(confidences) > 0:
            confidences = confidences / np.sum(confidences)
        else:
            confidences = np.ones(len(confidences)) / len(confidences)

        # 加权平均
        weighted_sum = np.zeros_like(masks[0], dtype=np.float32)
        for mask, conf in zip(masks, confidences):
            weighted_sum += mask.astype(np.float32) * conf

        # 使用自适应阈值而不是固定0.5
        # 根据置信度分布调整阈值
        avg_confidence = np.mean(confidences)
        adaptive_threshold = max(0.3, min(0.7, avg_confidence))

        fused_mask = weighted_sum > adaptive_threshold

        # 应用形态学处理和边缘平滑
        return self._smooth_mask(fused_mask.astype(bool))

    def _smooth_mask(self, mask: np.ndarray) -> np.ndarray:
        """
        掩膜平滑处理，减少边缘抖动

        Args:
            mask: 输入掩膜

        Returns:
            平滑后的掩膜
        """
        if mask is None or mask.size == 0:
            return mask

        try:
            # 转换为uint8格式
            mask_uint8 = mask.astype(np.uint8) * 255

            # 1. 形态学开运算：去除小噪点
            kernel_small = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            mask_opened = cv2.morphologyEx(mask_uint8, cv2.MORPH_OPEN, kernel_small)

            # 2. 形态学闭运算：填补小空洞
            kernel_medium = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            mask_closed = cv2.morphologyEx(mask_opened, cv2.MORPH_CLOSE, kernel_medium)

            # 3. 高斯模糊 + 阈值化：平滑边缘
            mask_blurred = cv2.GaussianBlur(mask_closed, (5, 5), 1.0)
            _, mask_smooth = cv2.threshold(mask_blurred, 127, 255, cv2.THRESH_BINARY)

            # 4. 轻微的膨胀操作：确保物体完整性
            kernel_dilate = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            mask_final = cv2.dilate(mask_smooth, kernel_dilate, iterations=1)

            return (mask_final > 0).astype(bool)

        except Exception as e:
            print(f"⚠️ 掩膜平滑处理失败: {e}")
            return mask.astype(bool)
    
    def get_latest_detection(self) -> Optional[Detection]:
        """获取最新的检测结果"""
        if self.detections_history:
            return self.detections_history[-1]
        return None


class MultiFrameYOLODetector:
    """
    多帧YOLO检测器
    实现连续多帧检测、掩膜时序融合、稳定ID跟踪和数量输出
    """
    
    def __init__(self, config: SystemConfig = None,
                 n_frames: int = 3,
                 stable_threshold: int = 2,
                 confidence_threshold: float = 0.15,
                 iou_threshold: float = 0.5,
                 fusion_method: str = "majority_vote"):
        """
        初始化多帧YOLO检测器

        Args:
            config: 系统配置
            n_frames: 用于融合的帧数（建议3-5）
            stable_threshold: 稳定物体阈值（连续出现≥K帧才计入最终数量，建议2-3）
            confidence_threshold: 置信度阈值（降低到0.15，让更多候选进入多帧处理）
            iou_threshold: IoU阈值（用于物体匹配，建议0.5）
            fusion_method: 融合方法（"majority_vote" 或 "confidence_weighted"）
        """
        if config is None:
            from config import default_config
            config = default_config

        self.config = config
        self.yolo_detector = YOLODetector(config)

        # 多帧检测参数
        self.n_frames = n_frames
        self.stable_threshold = stable_threshold
        self.confidence_threshold = confidence_threshold
        self.iou_threshold = iou_threshold
        self.fusion_method = fusion_method

        # 稳定性判断参数
        self.min_stable_confidence = getattr(config.multi_frame_detection, 'min_stable_confidence', 0.3)
        self.iou_stability_threshold = getattr(config.multi_frame_detection, 'iou_stability_threshold', 0.3)
        self.enable_iou_stability_check = getattr(config.multi_frame_detection, 'enable_iou_stability_check', True)

        # 跟踪状态
        self.tracked_objects: Dict[str, TrackedObject] = {}
        self.frame_count = 0
        self.max_missing_frames = 5  # 物体消失超过此帧数后从跟踪列表中移除

        print(f"🎯 多帧YOLO检测器初始化完成:")
        print(f"   融合帧数: {self.n_frames}")
        print(f"   稳定阈值: {self.stable_threshold} 帧")
        print(f"   初始置信度阈值: {self.confidence_threshold} (宽松过滤)")
        print(f"   稳定物体最低置信度: {self.min_stable_confidence}")
        print(f"   IoU阈值: {self.iou_threshold}")
        print(f"   IoU稳定性检查: {'启用' if self.enable_iou_stability_check else '禁用'}")
        print(f"   融合方法: {self.fusion_method}")
    
    def detect_objects_multi_frame(self, image: np.ndarray) -> Tuple[List[Detection], List[Detection]]:
        """
        多帧物体检测
        
        Args:
            image: 输入图像
            
        Returns:
            Tuple[当前帧所有检测结果, 稳定物体检测结果]
        """
        start_time = time.time()
        
        # 1. 单帧YOLO检测
        raw_detections = self.yolo_detector.detect_objects(image)
        
        # 2. 过滤低置信度检测
        filtered_detections = [
            det for det in raw_detections 
            if det.confidence >= self.confidence_threshold
        ]
        
        if len(filtered_detections) != len(raw_detections):
            print(f"🔍 置信度过滤: {len(raw_detections)} → {len(filtered_detections)} 个检测结果")
        
        # 3. 更新跟踪状态
        self._update_tracking(filtered_detections)
        
        # 4. 获取稳定物体的融合检测结果
        stable_detections = self._get_stable_detections()
        
        # 5. 清理过期的跟踪对象
        self._cleanup_tracking()
        
        self.frame_count += 1
        
        detection_time = time.time() - start_time
        print(f"🎯 多帧检测完成: 当前帧{len(filtered_detections)}个, 稳定物体{len(stable_detections)}个 (用时: {detection_time:.3f}s)")
        
        return filtered_detections, stable_detections
    
    def _update_tracking(self, detections: List[Detection]):
        """更新物体跟踪状态（增强版本，包含IoU稳定性检查）"""
        current_frame_objects = set()

        for detection in detections:
            # 寻找最匹配的跟踪对象
            best_match_id = self._find_best_match(detection)

            if best_match_id:
                # 更新现有跟踪对象
                tracked_obj = self.tracked_objects[best_match_id]

                # IoU稳定性检查
                is_stable_movement = True
                if self.enable_iou_stability_check:
                    is_stable_movement = tracked_obj.check_iou_stability(
                        detection, self.iou_stability_threshold
                    )

                if is_stable_movement:
                    # 稳定移动，正常更新
                    tracked_obj.add_detection(detection, self.frame_count)
                    current_frame_objects.add(best_match_id)

                    # 检查是否达到稳定阈值，并且平均置信度足够高
                    if (tracked_obj.consecutive_frames >= self.stable_threshold and
                        tracked_obj.get_average_confidence() >= self.min_stable_confidence):
                        tracked_obj.is_stable = True
                    else:
                        tracked_obj.is_stable = False
                else:
                    # 位置跳动过大，重置稳定计数但保持跟踪
                    print(f"   ⚠️ 物体 {tracked_obj.class_name} (ID:{best_match_id[:4]}) 位置跳动过大，重置稳定计数")
                    tracked_obj.consecutive_frames = 1  # 重置为1（当前帧）
                    tracked_obj.is_stable = False
                    tracked_obj.add_detection(detection, self.frame_count)
                    current_frame_objects.add(best_match_id)
            else:
                # 创建新的跟踪对象
                new_id = str(uuid.uuid4())[:8]
                new_tracked_obj = TrackedObject(
                    object_id=new_id,
                    class_name=detection.class_name,
                    class_id=detection.class_id
                )
                new_tracked_obj.add_detection(detection, self.frame_count)
                self.tracked_objects[new_id] = new_tracked_obj
                current_frame_objects.add(new_id)

        # 更新未在当前帧出现的跟踪对象
        for obj_id, tracked_obj in self.tracked_objects.items():
            if obj_id not in current_frame_objects:
                tracked_obj.consecutive_frames = 0  # 重置连续帧计数
                tracked_obj.is_stable = False  # 标记为不稳定
    
    def _find_best_match(self, detection: Detection) -> Optional[str]:
        """寻找与当前检测最匹配的跟踪对象（改进版本）"""
        best_match_id = None
        best_score = 0.0

        for obj_id, tracked_obj in self.tracked_objects.items():
            # 类别必须匹配
            if tracked_obj.class_name != detection.class_name:
                continue

            # 计算匹配分数（综合IoU、置信度、时间衰减等因素）
            match_score = self._calculate_match_score(detection, tracked_obj)

            if match_score > self.iou_threshold and match_score > best_score:
                best_score = match_score
                best_match_id = obj_id

        return best_match_id

    def _calculate_match_score(self, detection: Detection, tracked_obj: TrackedObject) -> float:
        """
        计算检测结果与跟踪对象的匹配分数
        综合考虑IoU、置信度、时间衰减等因素
        """
        latest_detection = tracked_obj.get_latest_detection()
        if latest_detection is None:
            return 0.0

        # 1. 基础IoU分数
        iou_score = self._calculate_bbox_iou(detection.bbox, latest_detection.bbox)

        # 2. 置信度分数（当前检测置信度越高，匹配分数越高）
        confidence_score = detection.confidence

        # 3. 时间衰减分数（物体消失时间越长，匹配分数越低）
        frames_since_last_seen = self.frame_count - tracked_obj.last_seen_frame
        time_decay = max(0.0, 1.0 - frames_since_last_seen / self.max_missing_frames)

        # 4. 稳定性奖励（稳定物体更容易匹配）
        stability_bonus = 0.1 if tracked_obj.is_stable else 0.0

        # 5. 掩膜相似度（如果都有掩膜）
        mask_similarity = 0.0
        if detection.mask is not None and len(tracked_obj.masks_history) > 0:
            latest_mask = tracked_obj.masks_history[-1]
            if latest_mask is not None:
                mask_similarity = self._calculate_mask_similarity(detection.mask, latest_mask)

        # 综合分数计算（加权平均）
        weights = {
            'iou': 0.5,
            'confidence': 0.2,
            'time_decay': 0.15,
            'stability': 0.05,
            'mask': 0.1
        }

        final_score = (
            weights['iou'] * iou_score +
            weights['confidence'] * confidence_score +
            weights['time_decay'] * time_decay +
            weights['stability'] * stability_bonus +
            weights['mask'] * mask_similarity
        )

        return final_score

    def _calculate_mask_similarity(self, mask1: np.ndarray, mask2: np.ndarray) -> float:
        """计算两个掩膜的相似度"""
        try:
            if mask1.shape != mask2.shape:
                # 调整掩膜尺寸
                mask2 = cv2.resize(mask2.astype(np.uint8),
                                 (mask1.shape[1], mask1.shape[0]),
                                 interpolation=cv2.INTER_NEAREST).astype(bool)

            # 计算Jaccard相似度（IoU）
            intersection = np.logical_and(mask1, mask2).sum()
            union = np.logical_or(mask1, mask2).sum()

            if union == 0:
                return 1.0 if intersection == 0 else 0.0

            return intersection / union

        except Exception as e:
            print(f"⚠️ 掩膜相似度计算失败: {e}")
            return 0.0
    
    def _calculate_bbox_iou(self, bbox1: Tuple[int, int, int, int],
                           bbox2: Tuple[int, int, int, int]) -> float:
        """计算两个边界框的IoU"""
        x1_1, y1_1, x2_1, y2_1 = bbox1
        x1_2, y1_2, x2_2, y2_2 = bbox2

        # 计算交集
        x1_inter = max(x1_1, x1_2)
        y1_inter = max(y1_1, y1_2)
        x2_inter = min(x2_1, x2_2)
        y2_inter = min(y2_1, y2_2)

        if x2_inter <= x1_inter or y2_inter <= y1_inter:
            return 0.0

        inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)

        # 计算并集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union_area = area1 + area2 - inter_area

        if union_area <= 0:
            return 0.0

        return inter_area / union_area
    
    def _get_stable_detections(self) -> List[Detection]:
        """获取稳定物体的融合检测结果"""
        stable_detections = []
        
        for tracked_obj in self.tracked_objects.values():
            if not tracked_obj.is_stable:
                continue
            
            # 获取融合后的掩膜
            fused_mask = tracked_obj.get_fused_mask(self.fusion_method, self.n_frames)
            
            # 获取最新的检测结果作为基础
            latest_detection = tracked_obj.get_latest_detection()
            if latest_detection is None:
                continue
            
            # 创建融合后的检测结果
            fused_detection = Detection(
                box=latest_detection.box,
                confidence=latest_detection.confidence,
                class_id=latest_detection.class_id,
                class_name=latest_detection.class_name,
                mask=fused_mask
            )
            
            stable_detections.append(fused_detection)
        
        return stable_detections
    
    def _cleanup_tracking(self):
        """清理过期的跟踪对象"""
        to_remove = []
        
        for obj_id, tracked_obj in self.tracked_objects.items():
            frames_since_last_seen = self.frame_count - tracked_obj.last_seen_frame
            if frames_since_last_seen > self.max_missing_frames:
                to_remove.append(obj_id)
        
        for obj_id in to_remove:
            del self.tracked_objects[obj_id]
        
        if to_remove:
            print(f"🧹 清理了 {len(to_remove)} 个过期跟踪对象")
    
    def get_tracking_stats(self) -> Dict[str, Any]:
        """获取跟踪统计信息"""
        total_objects = len(self.tracked_objects)
        stable_objects = sum(1 for obj in self.tracked_objects.values() if obj.is_stable)
        
        class_counts = {}
        stable_class_counts = {}
        
        for obj in self.tracked_objects.values():
            class_counts[obj.class_name] = class_counts.get(obj.class_name, 0) + 1
            if obj.is_stable:
                stable_class_counts[obj.class_name] = stable_class_counts.get(obj.class_name, 0) + 1
        
        return {
            'frame_count': self.frame_count,
            'total_tracked_objects': total_objects,
            'stable_objects': stable_objects,
            'class_counts': class_counts,
            'stable_class_counts': stable_class_counts,
            'fusion_method': self.fusion_method,
            'n_frames': self.n_frames,
            'stable_threshold': self.stable_threshold
        }
    
    def reset_tracking(self):
        """重置跟踪状态"""
        self.tracked_objects.clear()
        self.frame_count = 0
        print("🔄 跟踪状态已重置")

    def get_detection_with_stable_masks(self, image: np.ndarray) -> List[Detection]:
        """
        获取带有稳定掩膜的检测结果（主要接口）
        这是向分析器传递稳定结果的主要方法

        Args:
            image: 输入图像

        Returns:
            稳定的检测结果列表（带有融合后的掩膜）
        """
        _, stable_detections = self.detect_objects_multi_frame(image)
        return stable_detections

    def visualize_tracking_status(self, image: np.ndarray) -> np.ndarray:
        """
        在图像上可视化跟踪状态

        Args:
            image: 输入图像

        Returns:
            带有跟踪状态标注的图像
        """
        if image is None:
            return None

        result_image = image.copy()

        # 颜色定义
        stable_color = (0, 255, 0)  # 绿色 - 稳定物体
        tracking_color = (0, 255, 255)  # 黄色 - 跟踪中物体
        new_color = (0, 0, 255)  # 红色 - 新物体

        for obj_id, tracked_obj in self.tracked_objects.items():
            latest_detection = tracked_obj.get_latest_detection()
            if latest_detection is None:
                continue

            # 选择颜色
            if tracked_obj.is_stable:
                color = stable_color
                status = f"STABLE({tracked_obj.consecutive_frames})"
            elif tracked_obj.consecutive_frames > 0:
                color = tracking_color
                status = f"TRACK({tracked_obj.consecutive_frames})"
            else:
                color = new_color
                status = "NEW"

            # 绘制边界框
            x1, y1, x2, y2 = latest_detection.bbox
            cv2.rectangle(result_image, (x1, y1), (x2, y2), color, 2)

            # 绘制标签
            label = f"{tracked_obj.class_name} {status} ID:{obj_id[:4]}"
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.5
            thickness = 1

            (text_width, text_height), baseline = cv2.getTextSize(label, font, font_scale, thickness)

            # 绘制文本背景
            cv2.rectangle(result_image,
                         (x1, y1 - text_height - baseline - 5),
                         (x1 + text_width, y1),
                         color, -1)

            # 绘制文本
            cv2.putText(result_image, label,
                       (x1, y1 - baseline - 2),
                       font, font_scale, (255, 255, 255), thickness)

            # 绘制融合掩膜（如果是稳定物体）
            if tracked_obj.is_stable:
                fused_mask = tracked_obj.get_fused_mask(self.fusion_method, self.n_frames)
                if fused_mask is not None:
                    # 创建彩色掩膜覆盖层
                    mask_overlay = np.zeros_like(result_image)
                    mask_overlay[fused_mask] = color

                    # 半透明叠加
                    alpha = 0.3
                    result_image = cv2.addWeighted(result_image, 1-alpha, mask_overlay, alpha, 0)

        # 添加统计信息
        stats = self.get_tracking_stats()
        info_text = [
            f"Frame: {stats['frame_count']}",
            f"Tracked: {stats['total_tracked_objects']}",
            f"Stable: {stats['stable_objects']}",
            f"Method: {stats['fusion_method']}"
        ]

        y_offset = 30
        for text in info_text:
            cv2.putText(result_image, text, (10, y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            y_offset += 25

        return result_image

    def save_tracking_results(self, save_path: str = None) -> str:
        """
        保存跟踪结果到文件

        Args:
            save_path: 保存路径

        Returns:
            保存的文件路径
        """
        import json
        from datetime import datetime

        if save_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = f"output/multi_frame_tracking_{timestamp}.json"

        # 准备保存数据
        tracking_data = {
            'timestamp': datetime.now().isoformat(),
            'stats': self.get_tracking_stats(),
            'tracked_objects': {}
        }

        for obj_id, tracked_obj in self.tracked_objects.items():
            tracking_data['tracked_objects'][obj_id] = {
                'class_name': tracked_obj.class_name,
                'class_id': tracked_obj.class_id,
                'consecutive_frames': tracked_obj.consecutive_frames,
                'is_stable': tracked_obj.is_stable,
                'last_seen_frame': tracked_obj.last_seen_frame,
                'detection_count': len(tracked_obj.detections_history)
            }

        # 保存到文件
        try:
            import os
            os.makedirs(os.path.dirname(save_path), exist_ok=True)

            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(tracking_data, f, indent=2, ensure_ascii=False)

            print(f"💾 跟踪结果已保存: {save_path}")
            return save_path

        except Exception as e:
            print(f"❌ 保存跟踪结果失败: {e}")
            return None


def test_multi_frame_detector():
    """测试多帧YOLO检测器"""
    print("🧪 测试多帧YOLO检测器...")

    try:
        # 创建检测器
        detector = MultiFrameYOLODetector(
            n_frames=3,
            stable_threshold=2,
            confidence_threshold=0.5,
            fusion_method="majority_vote"
        )

        # 创建测试图像序列
        test_images = []
        for i in range(5):
            test_image = np.zeros((480, 640, 3), dtype=np.uint8)

            # 添加一些移动的彩色方块来模拟物体
            x_offset = i * 20
            test_image[100:200, 100+x_offset:200+x_offset] = [255, 0, 0]  # 红色方块
            test_image[300:400, 400-x_offset:500-x_offset] = [0, 255, 0]  # 绿色方块

            test_images.append(test_image)

        print("🔍 运行多帧检测测试...")

        all_detections = []
        stable_detections_list = []

        for i, image in enumerate(test_images):
            print(f"\n--- 处理第 {i+1} 帧 ---")
            current_detections, stable_detections = detector.detect_objects_multi_frame(image)

            all_detections.append(current_detections)
            stable_detections_list.append(stable_detections)

            # 显示统计信息
            stats = detector.get_tracking_stats()
            print(f"当前帧检测: {len(current_detections)} 个")
            print(f"稳定物体: {len(stable_detections)} 个")
            print(f"跟踪对象总数: {stats['total_tracked_objects']}")
            print(f"稳定对象总数: {stats['stable_objects']}")

        # 测试可视化功能
        if test_images:
            print("\n🎨 测试可视化功能...")
            visualized_image = detector.visualize_tracking_status(test_images[-1])
            if visualized_image is not None:
                print("✅ 可视化功能正常")

        # 测试保存功能
        print("\n💾 测试保存功能...")
        save_path = detector.save_tracking_results()
        if save_path:
            print(f"✅ 保存功能正常: {save_path}")

        # 显示最终统计
        final_stats = detector.get_tracking_stats()
        print(f"\n📊 最终统计:")
        print(f"   处理帧数: {final_stats['frame_count']}")
        print(f"   跟踪对象: {final_stats['total_tracked_objects']}")
        print(f"   稳定对象: {final_stats['stable_objects']}")
        print(f"   类别统计: {final_stats['class_counts']}")
        print(f"   稳定类别统计: {final_stats['stable_class_counts']}")

        print("✅ 多帧检测器测试完成")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_multi_frame_detector()
